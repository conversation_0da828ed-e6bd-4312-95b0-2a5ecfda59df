{% extends 'dashboard/base.html' %}
{% load static %}

{% block content %}
<div class="page-body">
  <div class="container-fluid">
    <div class="page-title">
      <div class="row">
        <div class="col-xl-4 col-sm-7 box-col-3">
          <h3>Complaint Management</h3>
        </div>
        <div class="col-xl-8 col-sm-5 box-col-9">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'dashboard' %}"><i data-feather="home"></i></a></li>
            <li class="breadcrumb-item active">Complaints</li>
          </ol>
        </div>
      </div>
    </div>
  </div>
  
  <div class="container-fluid">
    <div class="row">
      <!-- Statistics Cards -->
      <div class="col-xl-3 col-sm-6">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="flex-shrink-0">
                <i data-feather="file-text" class="feather-32 text-primary"></i>
              </div>
              <div class="flex-grow-1 ms-3">
                <h4 class="mb-0">{{ total_complaints|default:0 }}</h4>
                <p class="text-muted mb-0">Total Complaints</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-xl-3 col-sm-6">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="flex-shrink-0">
                <i data-feather="clock" class="feather-32 text-warning"></i>
              </div>
              <div class="flex-grow-1 ms-3">
                <h4 class="mb-0">{{ pending_complaints|default:0 }}</h4>
                <p class="text-muted mb-0">Pending</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-xl-3 col-sm-6">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="flex-shrink-0">
                <i data-feather="activity" class="feather-32 text-info"></i>
              </div>
              <div class="flex-grow-1 ms-3">
                <h4 class="mb-0">{{ in_progress_complaints|default:0 }}</h4>
                <p class="text-muted mb-0">In Progress</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-xl-3 col-sm-6">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="flex-shrink-0">
                <i data-feather="check-circle" class="feather-32 text-success"></i>
              </div>
              <div class="flex-grow-1 ms-3">
                <h4 class="mb-0">{{ resolved_complaints|default:0 }}</h4>
                <p class="text-muted mb-0">Resolved</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Quick Actions -->
      <div class="col-xl-12">
        <div class="card">
          <div class="card-header">
            <h4 class="card-title mb-0">Quick Actions</h4>
          </div>
          <div class="card-body">
            <div class="row">
              <div class="col-md-3">
                <a href="{% url 'submit_complaint' %}" class="btn btn-primary btn-lg w-100 mb-2">
                  <i data-feather="plus-circle"></i> Submit New Complaint
                </a>
              </div>
              <div class="col-md-3">
                <button class="btn btn-info btn-lg w-100 mb-2" data-bs-toggle="modal" data-bs-target="#trackModal">
                  <i data-feather="search"></i> Track Complaint
                </button>
              </div>
              <div class="col-md-3">
                <a href="#" class="btn btn-warning btn-lg w-100 mb-2">
                  <i data-feather="filter"></i> Filter Complaints
                </a>
              </div>
              <div class="col-md-3">
                <a href="#" class="btn btn-success btn-lg w-100 mb-2">
                  <i data-feather="download"></i> Export Report
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Complaints Table -->
      <div class="col-xl-12">
        <div class="card">
          <div class="card-header">
            <h4 class="card-title mb-0">All Complaints</h4>
            <div class="card-header-right">
              <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary active">All</button>
                <button type="button" class="btn btn-outline-warning">Pending</button>
                <button type="button" class="btn btn-outline-info">In Progress</button>
                <button type="button" class="btn btn-outline-success">Resolved</button>
              </div>
            </div>
          </div>
          <div class="card-body">
            {% if complaints %}
              <div class="table-responsive">
                <table class="table table-striped table-hover">
                  <thead>
                    <tr>
                      <th>Reference</th>
                      <th>Complainant</th>
                      <th>Department</th>
                      <th>Type</th>
                      <th>Status</th>
                      <th>Priority</th>
                      <th>Submitted</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for complaint in complaints %}
                    <tr>
                      <td><code>{{ complaint.reference }}</code></td>
                      <td>
                        <div class="d-flex align-items-center">
                          <img class="img-fluid rounded-circle me-2" src="{% static 'assets/images/dashboard/profile.png' %}" alt="Profile" width="30">
                          <div>
                            <strong>{{ complaint.name|default:complaint.user.get_full_name|default:"Anonymous" }}</strong>
                            <br><small class="text-muted">{{ complaint.email|default:complaint.user.email }}</small>
                          </div>
                        </div>
                      </td>
                      <td>{{ complaint.department.name }}</td>
                      <td>{{ complaint.type }}</td>
                      <td>
                        <span class="badge badge-{% if complaint.status == 'pending' %}warning{% elif complaint.status == 'in_progress' %}info{% elif complaint.status == 'resolved' %}success{% else %}danger{% endif %}">
                          {{ complaint.get_status_display }}
                        </span>
                      </td>
                      <td>
                        <span class="badge badge-outline-primary">Normal</span>
                      </td>
                      <td>{{ complaint.created_at|date:"M d, Y H:i" }}</td>
                      <td>
                        <div class="btn-group" role="group">
                          <a href="{% url 'track_complaint' complaint.reference %}" class="btn btn-sm btn-primary" title="View Details">
                            <i data-feather="eye"></i>
                          </a>
                          <button class="btn btn-sm btn-warning" title="Edit Status">
                            <i data-feather="edit"></i>
                          </button>
                          <button class="btn btn-sm btn-info" title="Add Response">
                            <i data-feather="message-circle"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
              
              <!-- Pagination -->
              <nav aria-label="Complaints pagination">
                <ul class="pagination justify-content-center">
                  <li class="page-item disabled">
                    <a class="page-link" href="#" tabindex="-1">Previous</a>
                  </li>
                  <li class="page-item active"><a class="page-link" href="#">1</a></li>
                  <li class="page-item"><a class="page-link" href="#">2</a></li>
                  <li class="page-item"><a class="page-link" href="#">3</a></li>
                  <li class="page-item">
                    <a class="page-link" href="#">Next</a>
                  </li>
                </ul>
              </nav>
            {% else %}
              <div class="text-center py-5">
                <i data-feather="inbox" class="feather-48 text-muted mb-3"></i>
                <h5 class="text-muted">No complaints found</h5>
                <p class="text-muted">No complaints have been submitted yet.</p>
                <a href="{% url 'submit_complaint' %}" class="btn btn-primary">
                  <i data-feather="plus-circle"></i> Submit First Complaint
                </a>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Track Complaint Modal -->
<div class="modal fade" id="trackModal" tabindex="-1" aria-labelledby="trackModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="trackModalLabel">Track Complaint</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <form>
          <div class="mb-3">
            <label for="referenceInput" class="form-label">Complaint Reference Number</label>
            <input type="text" class="form-control" id="referenceInput" placeholder="Enter reference number">
          </div>
        </form>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
        <button type="button" class="btn btn-primary">Track Complaint</button>
      </div>
    </div>
  </div>
</div>
{% endblock %}
