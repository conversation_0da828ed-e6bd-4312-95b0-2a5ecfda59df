{% extends 'dashboard/base.html' %}
{% load static %}

{% block content %}
<div class="page-body">
  <div class="container-fluid">
    <div class="page-title">
      <div class="row">
        <div class="col-xl-4 col-sm-7 box-col-3">
          <h3>Feedback Management</h3>
        </div>
        <div class="col-xl-8 col-sm-5 box-col-9">
          <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'dashboard' %}"><i data-feather="home"></i></a></li>
            <li class="breadcrumb-item active">Feedback</li>
          </ol>
        </div>
      </div>
    </div>
  </div>
  
  <div class="container-fluid">
    <div class="row">
      <!-- Feedback Statistics -->
      <div class="col-xl-3 col-sm-6">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="flex-shrink-0">
                <i data-feather="star" class="feather-32 text-warning"></i>
              </div>
              <div class="flex-grow-1 ms-3">
                <h4 class="mb-0">{{ total_feedback|default:0 }}</h4>
                <p class="text-muted mb-0">Total Feedback</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-xl-3 col-sm-6">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="flex-shrink-0">
                <i data-feather="trending-up" class="feather-32 text-success"></i>
              </div>
              <div class="flex-grow-1 ms-3">
                <h4 class="mb-0">{{ average_rating|default:"0.0" }}</h4>
                <p class="text-muted mb-0">Average Rating</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-xl-3 col-sm-6">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="flex-shrink-0">
                <i data-feather="thumbs-up" class="feather-32 text-primary"></i>
              </div>
              <div class="flex-grow-1 ms-3">
                <h4 class="mb-0">{{ positive_feedback|default:0 }}</h4>
                <p class="text-muted mb-0">Positive (4-5★)</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="col-xl-3 col-sm-6">
        <div class="card">
          <div class="card-body">
            <div class="d-flex align-items-center">
              <div class="flex-shrink-0">
                <i data-feather="thumbs-down" class="feather-32 text-danger"></i>
              </div>
              <div class="flex-grow-1 ms-3">
                <h4 class="mb-0">{{ negative_feedback|default:0 }}</h4>
                <p class="text-muted mb-0">Negative (1-2★)</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Rating Distribution -->
      <div class="col-xl-6">
        <div class="card">
          <div class="card-header">
            <h4 class="card-title mb-0">Rating Distribution</h4>
          </div>
          <div class="card-body">
            <div class="rating-breakdown">
              <div class="d-flex align-items-center mb-2">
                <span class="me-2">5★</span>
                <div class="progress flex-grow-1 me-2">
                  <div class="progress-bar bg-success" role="progressbar" style="width: 45%"></div>
                </div>
                <span class="text-muted">45%</span>
              </div>
              <div class="d-flex align-items-center mb-2">
                <span class="me-2">4★</span>
                <div class="progress flex-grow-1 me-2">
                  <div class="progress-bar bg-info" role="progressbar" style="width: 30%"></div>
                </div>
                <span class="text-muted">30%</span>
              </div>
              <div class="d-flex align-items-center mb-2">
                <span class="me-2">3★</span>
                <div class="progress flex-grow-1 me-2">
                  <div class="progress-bar bg-warning" role="progressbar" style="width: 15%"></div>
                </div>
                <span class="text-muted">15%</span>
              </div>
              <div class="d-flex align-items-center mb-2">
                <span class="me-2">2★</span>
                <div class="progress flex-grow-1 me-2">
                  <div class="progress-bar bg-orange" role="progressbar" style="width: 7%"></div>
                </div>
                <span class="text-muted">7%</span>
              </div>
              <div class="d-flex align-items-center">
                <span class="me-2">1★</span>
                <div class="progress flex-grow-1 me-2">
                  <div class="progress-bar bg-danger" role="progressbar" style="width: 3%"></div>
                </div>
                <span class="text-muted">3%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Recent Feedback Summary -->
      <div class="col-xl-6">
        <div class="card">
          <div class="card-header">
            <h4 class="card-title mb-0">Feedback Trends</h4>
          </div>
          <div class="card-body">
            <div class="row text-center">
              <div class="col-4">
                <h5 class="text-success">{{ this_month_feedback|default:0 }}</h5>
                <p class="text-muted mb-0">This Month</p>
              </div>
              <div class="col-4">
                <h5 class="text-info">{{ last_month_feedback|default:0 }}</h5>
                <p class="text-muted mb-0">Last Month</p>
              </div>
              <div class="col-4">
                <h5 class="text-warning">{{ improvement_suggestions|default:0 }}</h5>
                <p class="text-muted mb-0">Suggestions</p>
              </div>
            </div>
            <hr>
            <div class="text-center">
              <span class="badge badge-success">+12% improvement</span>
              <p class="text-muted mt-2">Compared to last month</p>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Feedback List -->
      <div class="col-xl-12">
        <div class="card">
          <div class="card-header">
            <h4 class="card-title mb-0">Recent Feedback</h4>
            <div class="card-header-right">
              <div class="btn-group" role="group">
                <button type="button" class="btn btn-outline-primary active">All</button>
                <button type="button" class="btn btn-outline-success">Positive</button>
                <button type="button" class="btn btn-outline-warning">Neutral</button>
                <button type="button" class="btn btn-outline-danger">Negative</button>
              </div>
            </div>
          </div>
          <div class="card-body">
            {% if feedback_list %}
              <div class="table-responsive">
                <table class="table table-striped">
                  <thead>
                    <tr>
                      <th>Complaint Ref</th>
                      <th>User</th>
                      <th>Rating</th>
                      <th>Comment</th>
                      <th>Department</th>
                      <th>Date</th>
                      <th>Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {% for feedback in feedback_list %}
                    <tr>
                      <td><code>{{ feedback.complaint.reference }}</code></td>
                      <td>
                        <div class="d-flex align-items-center">
                          <img class="img-fluid rounded-circle me-2" src="{% static 'assets/images/dashboard/profile.png' %}" alt="Profile" width="30">
                          <div>
                            <strong>{{ feedback.complaint.user.get_full_name|default:feedback.complaint.name|default:"Anonymous" }}</strong>
                          </div>
                        </div>
                      </td>
                      <td>
                        <div class="rating">
                          {% for i in "12345" %}
                            {% if forloop.counter <= feedback.rating %}
                              <i class="fa fa-star text-warning"></i>
                            {% else %}
                              <i class="fa fa-star-o text-muted"></i>
                            {% endif %}
                          {% endfor %}
                          <span class="ms-1">({{ feedback.rating }}/5)</span>
                        </div>
                      </td>
                      <td>
                        <div class="comment-preview">
                          {{ feedback.comment|truncatewords:10|default:"No comment provided" }}
                        </div>
                      </td>
                      <td>{{ feedback.complaint.department.name }}</td>
                      <td>{{ feedback.created_at|date:"M d, Y" }}</td>
                      <td>
                        <div class="btn-group" role="group">
                          <button class="btn btn-sm btn-primary" title="View Full Feedback" data-bs-toggle="modal" data-bs-target="#feedbackModal{{ feedback.id }}">
                            <i data-feather="eye"></i>
                          </button>
                          <button class="btn btn-sm btn-success" title="Mark as Reviewed">
                            <i data-feather="check"></i>
                          </button>
                          <button class="btn btn-sm btn-info" title="Respond">
                            <i data-feather="message-circle"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                    {% endfor %}
                  </tbody>
                </table>
              </div>
            {% else %}
              <div class="text-center py-5">
                <i data-feather="message-square" class="feather-48 text-muted mb-3"></i>
                <h5 class="text-muted">No feedback available</h5>
                <p class="text-muted">No feedback has been submitted yet.</p>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
