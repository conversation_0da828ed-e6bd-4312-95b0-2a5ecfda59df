<!DOCTYPE html>
<html lang="en">
  
<!-- Mirrored from admin.pixelstrap.net/zono/template/datatable-API.html by HTTrack Website Copier/3.x [XR&CO'2014], Thu, 17 Jul 2025 13:40:31 GMT -->
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Zono admin is super flexible, powerful, clean &amp; modern responsive bootstrap 5 admin template with unlimited possibilities.">
    <meta name="keywords" content="admin template, Zono admin template, dashboard template, flat admin template, responsive admin template, web app">
    <meta name="author" content="pixelstrap">
    <link rel="icon" href="../assets/images/favicon.png" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/images/favicon.png" type="image/x-icon">
    <title>Zono - Premium Admin Template</title>
    <!-- Google font -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
    <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@200;300;400;600;700;800;900&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,500,500i,700,700i,900&amp;display=swap" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="../assets/css/font-awesome.css">
    <!-- ico-font-->
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/icofont.css">
    <!-- Themify icon-->
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/themify.css">
    <!-- Flag icon-->
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/flag-icon.css">
    <!-- Feather icon-->
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/feather-icon.css">
    <!-- Plugins css start-->
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/slick.css">
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/slick-theme.css">
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/scrollbar.css">
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/animate.css">
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/datatables.css">
    <!-- Plugins css Ends-->
    <!-- Bootstrap css-->
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/bootstrap.css">
    <!-- App css-->
    <link rel="stylesheet" type="text/css" href="../assets/css/style.css">
    <link id="color" rel="stylesheet" href="../assets/css/color-1.css" media="screen">
    <!-- Responsive css-->
    <link rel="stylesheet" type="text/css" href="../assets/css/responsive.css">
  </head>
  <body> 
    <!-- loader starts-->
    <div class="loader-wrapper">
      <div class="theme-loader">    
        <div class="loader-p"></div>
      </div>
    </div>
    <!-- loader ends-->
    <!-- tap on top starts-->
    <div class="tap-top"><i data-feather="chevrons-up"></i></div>
    <!-- tap on tap ends-->
    <!-- page-wrapper Start-->
    <div class="page-wrapper compact-wrapper" id="pageWrapper">
      <!-- Page Header Start-->
      <div class="page-header">
        <div class="header-wrapper row m-0">
          <div class="header-logo-wrapper col-auto p-0">
            <div class="logo-wrapper"><a href="index-2.html"> <img class="img-fluid for-light" src="../assets/images/logo/logo.png" alt=""><img class="img-fluid for-dark" src="../assets/images/logo/logo_dark.png" alt=""></a></div>
            <div class="toggle-sidebar">
              <svg class="sidebar-toggle"> 
                <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-animation"></use>
              </svg>
            </div>
          </div>
          <form class="col-sm-4 form-inline search-full d-none d-xl-block" action="#" method="get">
            <div class="form-group">
              <div class="Typeahead Typeahead--twitterUsers">
                <div class="u-posRelative">
                  <input class="demo-input Typeahead-input form-control-plaintext w-100" type="text" placeholder="Type to Search .." name="q" title="" autofocus>
                  <svg class="search-bg svg-color">
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#search"></use>
                  </svg>
                </div>
              </div>
            </div>
          </form>
          <div class="nav-right col-xl-8 col-lg-12 col-auto pull-right right-header p-0">
            <ul class="nav-menus">
              <li class="serchinput">
                <div class="serchbox">
                  <svg>
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#search"></use>
                  </svg>
                </div>
                <div class="form-group search-form">
                  <input type="text" placeholder="Search here...">
                </div>
              </li>
              <li class="onhover-dropdown"> 
                <div class="notification-box">
                  <svg> 
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#Bell"></use>
                  </svg>
                </div>
                <div class="onhover-show-div notification-dropdown"> 
                  <h6 class="f-18 mb-0 dropdown-title">Notifications</h6>
                  <div class="notification-card">
                    <ul>
                      <li>
                        <div class="user-notification">
                          <div><img src="../assets/images/avtar/2.jpg" alt="avatar"></div>
                          <div class="user-description"><a href="letter-box.html">
                              <h4>You have new finical page design.</h4></a><span>Today 11:45pm</span></div>
                        </div>
                        <div class="notification-btn">
                          <button class="btn btn-pill btn-primary" type="button" title="btn btn-pill btn-primary">Accpet</button>
                          <button class="btn btn-pill btn-secondary" type="button" title="btn btn-pill btn-primary">Decline</button>
                        </div>
                        <div class="show-btn"><a href="index-2.html"> <span>Show</span></a></div>
                      </li>
                      <li>
                        <div class="user-notification">
                          <div><img src="../assets/images/avtar/17.jpg" alt="avatar"></div>
                          <div class="user-description"><a href="letter-box.html">
                              <h4>Congrats! you all task for today.</h4></a><span>Today 01:05pm</span></div>
                        </div>
                        <div class="notification-btn">
                          <button class="btn btn-pill btn-primary" type="button" title="btn btn-pill btn-primary">Accpet</button>
                          <button class="btn btn-pill btn-secondary" type="button" title="btn btn-pill btn-primary">Decline</button>
                        </div>
                        <div class="show-btn"><a href="index-2.html"> <span>Show</span></a></div>
                      </li>
                      <li> 
                        <div class="user-notification">
                          <div> <img src="../assets/images/avtar/18.jpg" alt="avatar"></div>
                          <div class="user-description"><a href="letter-box.html">
                              <h4>You have new in landing page design.</h4></a><span>Today 06:55pm</span></div>
                        </div>
                        <div class="notification-btn">
                          <button class="btn btn-pill btn-primary" type="button" title="btn btn-pill btn-primary">Accpet</button>
                          <button class="btn btn-pill btn-secondary" type="button" title="btn btn-pill btn-primary">Decline</button>
                        </div>
                        <div class="show-btn"><a href="index-2.html"> <span>Show</span></a></div>
                      </li>
                      <li>
                        <div class="user-notification">
                          <div><img src="../assets/images/avtar/19.jpg" alt="avatar"></div>
                          <div class="user-description"><a href="letter-box.html">
                              <h4>Congrats! you all task for today.</h4></a><span>Today 06:55pm</span></div>
                        </div>
                        <div class="notification-btn">
                          <button class="btn btn-pill btn-primary" type="button" title="btn btn-pill btn-primary">Accpet</button>
                          <button class="btn btn-pill btn-secondary" type="button" title="btn btn-pill btn-primary">Decline</button>
                        </div>
                        <div class="show-btn"> <a href="index-2.html"> <span>Show</span></a></div>
                      </li>
                      <li> <a class="f-w-700" href="letter-box.html">Check all </a></li>
                    </ul>
                  </div>
                </div>
              </li>
              <li class="onhover-dropdown">
                <svg>
                  <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#Bookmark"></use>
                </svg>
                <div class="onhover-show-div bookmark-flip">
                  <div class="flip-card">
                    <div class="flip-card-inner">
                      <div class="front">
                        <h6 class="f-18 mb-0 dropdown-title">Bookmark</h6>
                        <ul class="bookmark-dropdown">
                          <li>
                            <div class="row">
                              <div class="col-4 text-center"><a href="form-validation.html">
                                  <div class="bookmark-content">
                                    <div class="bookmark-icon bg-light-primary"><i data-feather="file-text"></i></div><span>Forms</span>
                                  </div></a></div>
                              <div class="col-4 text-center"><a href="user-profile.html">
                                  <div class="bookmark-content"> 
                                    <div class="bookmark-icon bg-light-secondary"><i data-feather="user"></i></div><span>Profile</span>
                                  </div></a></div>
                              <div class="col-4 text-center"><a href="bootstrap-basic-table.html">
                                  <div class="bookmark-content">
                                    <div class="bookmark-icon bg-light-warning"> <i data-feather="server"> </i></div><span>Tables </span>
                                  </div></a></div>
                            </div>
                          </li>
                          <li class="text-centermedia-body"> <a class="flip-btn f-w-700" id="flip-btn" href="javascript:void(0)">Add New Bookmark</a></li>
                        </ul>
                      </div>
                      <div class="back">
                        <ul>
                          <li>
                            <div class="bookmark-dropdown flip-back-content">
                              <input type="text" placeholder="search...">
                            </div>
                          </li>
                          <li><a class="f-w-700 d-block flip-back" id="flip-back" href="javascript:void(0)">Back</a></li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
              <li class="onhover-dropdown"> 
                <div class="message position-relative">
                  <svg>
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#Message"></use>
                  </svg><span class="rounded-pill badge-danger"></span>
                </div>
                <div class="onhover-show-div message-dropdown">
                  <h6 class="f-18 mb-0 dropdown-title">Message                               </h6>
                  <ul>
                    <li>
                      <div class="d-flex align-items-start">
                        <div class="message-img bg-light-primary"><img src="../assets/images/user/3.jpg" alt=""></div>
                        <div class="flex-grow-1">
                          <h5><a href="letter-box.html">Emay Walter</a></h5>
                          <p>Do you want to go see movie?</p>
                        </div>
                        <div class="notification-right"><i data-feather="x"></i></div>
                      </div>
                    </li>
                    <li>
                      <div class="d-flex align-items-start">
                        <div class="message-img bg-light-primary"><img src="../assets/images/user/6.jpg" alt=""></div>
                        <div class="flex-grow-1">
                          <h5> <a href="letter-box.html">Jason Borne</a></h5>
                          <p>Thank you for rating us.</p>
                        </div>
                        <div class="notification-right"><i data-feather="x"></i></div>
                      </div>
                    </li>
                    <li>
                      <div class="d-flex align-items-start"> 
                        <div class="message-img bg-light-primary"><img src="../assets/images/user/10.jpg" alt=""></div>
                        <div class="flex-grow-1">
                          <h5> <a href="letter-box.html">Sarah Loren</a></h5>
                          <p>What`s the project report update?</p>
                        </div>
                        <div class="notification-right"><i data-feather="x"></i></div>
                      </div>
                    </li>
                    <li> <a class="f-w-700" href="private-chat.html">Check all</a></li>
                  </ul>
                </div>
              </li>
              <li class="cart-nav onhover-dropdown">
                <div class="cart-box"> 
                  <svg>
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#Buy"></use>
                  </svg>
                </div>
                <div class="cart-dropdown onhover-show-div">
                  <h6 class="f-18 mb-0 dropdown-title">Cart</h6>
                  <ul>
                    <li>
                      <div class="d-flex"><img class="img-fluid b-r-5 img-50" src="../assets/images/ecommerce/05.jpg" alt="">
                        <div class="flex-grow-1"> <span>Women's Track Suit</span>
                          <h6 class="font-primary">8 x $65.00</h6>
                        </div>
                        <div class="close-circle"><a class="bg-primary" href="#"><i data-feather="x"></i></a></div>
                      </div>
                    </li>
                    <li>
                      <div class="d-flex"><img class="img-fluid b-r-5 img-50" src="../assets/images/ecommerce/02.jpg" alt="">
                        <div class="flex-grow-1"><span>Men's Jacket</span>
                          <h6 class="font-primary">10 x $50.00</h6>
                        </div>
                        <div class="close-circle"><a class="bg-primary" href="#"><i data-feather="x"></i></a></div>
                      </div>
                    </li>
                    <li class="total">
                      <h6 class="mb-0">Order Total :<span class="f-right">$1020.00</span></h6>
                    </li>
                    <li class="text-center"> <a href="cart.html">
                        <button class="btn btn-outline-primary" type="button">View Cart</button></a><a class="btn btn-primary view-checkout" href="checkout.html">Checkout  </a></li>
                  </ul>
                </div>
              </li>
              <li>
                <div class="mode">
                  <svg class="for-dark">
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#moon"></use>
                  </svg>
                  <svg class="for-light">
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#Sun"></use>
                  </svg>
                </div>
              </li>
              <li class="language-nav">
                <div class="translate_wrapper">
                  <div class="current_lang">
                    <div class="lang"><i class="flag-icon flag-icon-gb"></i><span class="lang-txt box-col-none">EN            </span></div>
                  </div>
                  <div class="more_lang">
                    <div class="lang selected" data-value="en"><i class="flag-icon flag-icon-us"></i><span class="lang-txt">English<span> (US)</span></span></div>
                    <div class="lang" data-value="de"><i class="flag-icon flag-icon-de"></i><span class="lang-txt">Deutsch</span></div>
                    <div class="lang" data-value="es"><i class="flag-icon flag-icon-es"></i><span class="lang-txt">Español</span></div>
                    <div class="lang" data-value="fr"><i class="flag-icon flag-icon-fr"></i><span class="lang-txt">Français</span></div>
                    <div class="lang" data-value="pt"><i class="flag-icon flag-icon-pt"></i><span class="lang-txt">Português<span> (BR)</span></span></div>
                    <div class="lang" data-value="cn"><i class="flag-icon flag-icon-cn"></i><span class="lang-txt">简体中文</span></div>
                    <div class="lang" data-value="ae"><i class="flag-icon flag-icon-ae"></i><span class="lang-txt">لعربية <span> (ae)</span></span></div>
                  </div>
                </div>
              </li>
              <li class="profile-nav onhover-dropdown pe-0 py-0">
                <div class="d-flex align-items-center profile-media"><img class="b-r-25" src="../assets/images/dashboard/profile.png" alt="">
                  <div class="flex-grow-1 user"><span>Helen Walter</span>
                    <p class="mb-0 font-nunito">Admin 
                      <svg>
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#header-arrow-down"></use>
                      </svg>
                    </p>
                  </div>
                </div>
                <ul class="profile-dropdown onhover-show-div">
                  <li><a href="user-profile.html"><i data-feather="user"></i><span>Account </span></a></li>
                  <li><a href="letter-box.html"><i data-feather="mail"></i><span>Inbox</span></a></li>
                  <li><a href="task.html"><i data-feather="file-text"></i><span>Taskboard</span></a></li>
                  <li><a href="edit-profile.html"><i data-feather="settings"></i><span>Settings</span></a></li>
                  <li><a href="login.html"> <i data-feather="log-in"></i><span>Log Out</span></a></li>
                </ul>
              </li>
            </ul>
          </div>
          <script class="result-template" type="text/x-handlebars-template">
            <div class="ProfileCard u-cf">              
            <div class="ProfileCard-avatar"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-airplay m-0"><path d="M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1"></path><polygon points="12 15 17 21 7 21 12 15"></polygon></svg></div>
            <div class="ProfileCard-details">
            <div class="ProfileCard-realName">{{name}}</div>
            </div>
            </div>
          </script>
          <script class="empty-template" type="text/x-handlebars-template"><div class="EmptyMessage">Your search turned up 0 results. This most likely means the backend is down, yikes!</div></script>
        </div>
      </div>
      <!-- Page Header Ends                              -->
      <!-- Page Body Start-->
      <div class="page-body-wrapper">
        <!-- Page Sidebar Start-->
        <div class="sidebar-wrapper" data-layout="stroke-svg">
          <div>
            <div class="logo-wrapper"><a href="index-2.html"> <img class="img-fluid for-light" src="../assets/images/logo/logo.png" alt=""><img class="img-fluid for-dark" src="../assets/images/logo/logo_dark.png" alt=""></a>
              <div class="toggle-sidebar">
                <svg class="sidebar-toggle"> 
                  <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#toggle-icon"></use>
                </svg>
              </div>
            </div>
            <div class="logo-icon-wrapper"><a href="index-2.html"><img class="img-fluid" src="../assets/images/logo/logo-icon.png" alt=""></a></div>
            <nav class="sidebar-main">
              <div class="left-arrow" id="left-arrow"><i data-feather="arrow-left"></i></div>
              <div id="sidebar-menu">
                <ul class="sidebar-links" id="simple-bar">
                  <li class="back-btn"><a href="index-2.html"><img class="img-fluid" src="../assets/images/logo/logo-icon.png" alt=""></a>
                    <div class="mobile-back text-end"><span>Back</span><i class="fa fa-angle-right ps-2" aria-hidden="true"></i></div>
                  </li>
                  <li class="pin-title sidebar-main-title">
                    <div> 
                      <h6>Pinned</h6>
                    </div>
                  </li>
                  <li class="sidebar-main-title">
                    <div>
                      <h6 class="lan-1">General</h6>
                    </div>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-home"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-home"></use>
                      </svg><span class="lan-3">Dashboard          </span></a>
                    <ul class="sidebar-submenu">
                      <li><a class="lan-4" href="index-2.html">Default</a></li>
                      <li><a class="lan-5" href="dashboard-02.html">Ecommerce</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-widget"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-widget"></use>
                      </svg><span class="lan-6">Widgets</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="general-widget.html">General</a></li>
                      <li> <a href="chart-widget.html">Chart</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"> <i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-layout"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-layout"></use>
                      </svg><span class="lan-7">Page layout</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="box-layout.html">Boxed</a></li>
                      <li><a href="layout-rtl.html">RTL</a></li>
                      <li><a href="layout-dark.html">Dark Layout</a></li>
                      <li><a href="hide-on-scroll.html">Hide Nav Scroll</a></li>
                      <li><a href="footer-light.html">Footer Light</a></li>
                      <li><a href="footer-dark.html">Footer Dark</a></li>
                      <li><a href="footer-fixed.html">Footer Fixed</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-main-title">
                    <div>
                      <h6 class="lan-8">Applications</h6>
                    </div>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack">    </i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-project"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-project"></use>
                      </svg><span>Project           </span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="projects.html">Project List</a></li>
                      <li><a href="projectcreate.html">Create new</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="file-manager.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-file"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-file"></use>
                      </svg><span>File manager</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack">        </i><a class="sidebar-link sidebar-title link-nav" href="kanban.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-board"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-board"></use>
                      </svg><span>kanban Board</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-ecommerce"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-ecommerce"></use>
                      </svg><span>Ecommerce</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="add-products.html">Add Product</a></li>
                      <li><a href="product.html">Product</a></li>
                      <li><a href="product-page.html">Product page</a></li>
                      <li><a href="list-products.html">Product list</a></li>
                      <li><a href="payment-details.html">Payment Details</a></li>
                      <li><a href="order-history.html">Order History</a></li>
                      <li><a class="submenu-title" href="#">Invoices
                          <h5 class="sub-arrow"><i class="fa fa-angle-right"></i></h5></a>
                        <ul class="submenu-content opensubmegamenu">
                          <li><a href="invoice-1.html">Invoice-1</a></li>
                          <li><a href="invoice-2.html">Invoice-2</a></li>
                          <li><a href="invoice-3.html">Invoice-3</a></li>
                          <li><a href="invoice-4.html">Invoice-4</a></li>
                          <li><a href="invoice-5.html">Invoice-5</a></li>
                          <li><a href="invoice-template.html">Invoice-6</a></li>
                        </ul>
                      </li>
                      <li><a href="cart.html">Cart</a></li>
                      <li><a href="list-wish.html">Wishlist</a></li>
                      <li><a href="checkout.html">Checkout</a></li>
                      <li><a href="pricing.html">Pricing          </a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"> </i><a class="sidebar-link sidebar-title link-nav" href="letter-box.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-email"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-email"></use>
                      </svg><span>Letter Box</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#"> 
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-chat"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-chat"></use>
                      </svg><span>Chat</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="private-chat.html">Private Chat</a></li>
                      <li><a href="group-chat.html">Group Chat</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-user"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-user"></use>
                      </svg><span>Users</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="user-profile.html">Users Profile</a></li>
                      <li><a href="edit-profile.html">Users Edit</a></li>
                      <li><a href="user-cards.html">Users Cards</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="bookmark.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-bookmark"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-bookmark"> </use>
                      </svg><span>Bookmarks </span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="contacts.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-contact"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-contact"> </use>
                      </svg><span>Contacts</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="task.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-task"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-task"> </use>
                      </svg><span>Tasks</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="calendar-basic.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-calendar"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-calender"></use>
                      </svg><span>Calendar</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="social-app.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-social"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-social"> </use>
                      </svg><span>Social App</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="to-do.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-to-do"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-to-do"> </use>
                      </svg><span>To-Do</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="search.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-search"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-search"> </use>
                      </svg><span>Search Result</span></a></li>
                  <li class="sidebar-main-title">
                    <div>
                      <h6>Forms & Table</h6>
                    </div>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-form"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-form"> </use>
                      </svg><span>Forms</span></a>
                    <ul class="sidebar-submenu">
                      <li><a class="submenu-title" href="#">Form Controls
                          <h5 class="sub-arrow"><i class="fa fa-angle-right"></i></h5></a>
                        <ul class="submenu-content opensubmegamenu">
                          <li><a href="form-validation.html">Form Validation</a></li>
                          <li><a href="base-input.html">Base Inputs</a></li>
                          <li><a href="radio-checkbox-control.html">Checkbox & Radio</a></li>
                          <li><a href="input-group.html">Input Groups</a></li>
                          <li> <a href="input-mask.html">Input Mask</a></li>
                          <li><a href="megaoptions.html">Mega Options</a></li>
                        </ul>
                      </li>
                      <li><a class="submenu-title" href="#">Form Widgets
                          <h5 class="sub-arrow"><i class="fa fa-angle-right"></i></h5></a>
                        <ul class="submenu-content opensubmegamenu">
                          <li><a href="datepicker.html">Datepicker</a></li>
                          <li><a href="touchspin.html">Touchspin</a></li>
                          <li><a href="select2.html">Select2</a></li>
                          <li><a href="switch.html">Switch</a></li>
                          <li><a href="typeahead.html">Typeahead</a></li>
                          <li><a href="clipboard.html">Clipboard</a></li>
                        </ul>
                      </li>
                      <li><a class="submenu-title" href="#">Form layout
                          <h5 class="sub-arrow"><i class="fa fa-angle-right"></i></h5></a>
                        <ul class="submenu-content opensubmegamenu">
                          <li><a href="form-wizard.html">Form Wizard 1</a></li>
                          <li><a href="form-wizard-two.html">Form Wizard 2</a></li>
                          <li><a href="two-factor.html">Two Factor</a></li>
                        </ul>
                      </li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-table"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-table"></use>
                      </svg><span>Tables</span></a>
                    <ul class="sidebar-submenu">
                      <li><a class="submenu-title" href="#">Bootstrap Tables
                          <h5 class="sub-arrow"><i class="fa fa-angle-right"></i></h5></a>
                        <ul class="submenu-content opensubmegamenu">
                          <li><a href="bootstrap-basic-table.html">Basic Tables</a></li>
                          <li><a href="table-components.html">Table components</a></li>
                        </ul>
                      </li>
                      <li><a class="submenu-title" href="#">Data Tables
                          <h5 class="sub-arrow"><i class="fa fa-angle-right"></i></h5></a>
                        <ul class="submenu-content opensubmegamenu">
                          <li><a href="datatable-basic-init.html">Basic Init</a></li>
                          <li> <a href="datatable-advance.html">Advance Init </a></li>
                          <li><a href="datatable-API.html">API</a></li>
                          <li><a href="datatable-data-source.html">Data Sources</a></li>
                        </ul>
                      </li>
                      <li><a href="datatable-ext-autofill.html">Ex. Data Tables</a></li>
                      <li><a href="jsgrid-table.html">Js Grid Table        </a></li>
                    </ul>
                  </li>
                  <li class="sidebar-main-title">
                    <div>
                      <h6>Components</h6>
                    </div>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-ui-kits"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-ui-kits"></use>
                      </svg><span>Ui Kits</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="typography.html">Typography</a></li>
                      <li><a href="avatars.html">Avatars</a></li>
                      <li><a href="helper-classes.html">helper classes</a></li>
                      <li><a href="grid.html">Grid</a></li>
                      <li><a href="tag-pills.html">Tag & pills</a></li>
                      <li><a href="progress-bar.html">Progress</a></li>
                      <li><a href="modal.html">Modal</a></li>
                      <li><a href="alert.html">Alert</a></li>
                      <li><a href="popover.html">Popover</a></li>
                      <li><a href="tooltip.html">Tooltip</a></li>
                      <li><a href="dropdown.html">Dropdown</a></li>
                      <li><a href="according.html">Accordion</a></li>
                      <li><a href="tab-bootstrap.html">Tabs</a></li>
                      <li><a href="list.html">Lists</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-bonus-kit"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-bonus-kit"></use>
                      </svg><span>Bonus Ui</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="scrollable.html">Scrollable</a></li>
                      <li><a href="tree.html">Tree view</a></li>
                      <li><a href="toasts.html">Toasts</a></li>
                      <li><a href="rating.html">Rating</a></li>
                      <li><a href="dropzone.html">dropzone</a></li>
                      <li><a href="tour.html">Tour</a></li>
                      <li><a href="sweet-alert2.html">SweetAlert2</a></li>
                      <li><a href="modal-animated.html">Animated Modal</a></li>
                      <li><a href="owl-carousel.html">Owl Carousel</a></li>
                      <li><a href="ribbons.html">Ribbons</a></li>
                      <li><a href="pagination.html">Pagination</a></li>
                      <li><a href="breadcrumb.html">Breadcrumb</a></li>
                      <li><a href="range-slider.html">Range Slider</a></li>
                      <li><a href="image-cropper.html">Image cropper</a></li>
                      <li><a href="basic-card.html">Basic Card</a></li>
                      <li><a href="creative-card.html">Creative Card</a></li>
                      <li><a href="dragable-card.html">Draggable Card</a></li>
                      <li><a href="timeline-v-1.html">Timeline </a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-animation"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-animation"></use>
                      </svg><span>Animation</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="animate.html">Animate</a></li>
                      <li><a href="scroll-reval.html">Scroll Reveal</a></li>
                      <li><a href="AOS.html">AOS animation</a></li>
                      <li><a href="tilt.html">Tilt Animation</a></li>
                      <li><a href="wow.html">Wow Animation</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-icons"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-icons"></use>
                      </svg><span>Icons</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="flag-icon.html">Flag icon</a></li>
                      <li><a href="font-awesome.html">Fontawesome Icon</a></li>
                      <li><a href="ico-icon.html">Ico Icon</a></li>
                      <li><a href="themify-icon.html">Themify Icon</a></li>
                      <li><a href="feather-icon.html">Feather icon</a></li>
                      <li><a href="whether-icon.html">Whether Icon</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-button"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-button"></use>
                      </svg><span>Buttons</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="buttons.html">Default Style</a></li>
                      <li><a href="button-group.html">Button Group</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-charts"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-charts"></use>
                      </svg><span>Charts</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="chart-apex.html">Apex Chart</a></li>
                      <li><a href="chart-google.html">Google Chart</a></li>
                      <li><a href="chart-sparkline.html">Sparkline chart</a></li>
                      <li><a href="chart-flot.html">Flot Chart</a></li>
                      <li><a href="chart-knob.html">Knob Chart</a></li>
                      <li><a href="chart-morris.html">Morris Chart</a></li>
                      <li><a href="chartjs.html">Chatjs Chart</a></li>
                      <li><a href="chartist.html">Chartist Chart</a></li>
                      <li><a href="chart-peity.html">Peity Chart</a></li>
                      <li><a href="echarts.html">Echarts</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-main-title">
                    <div>
                      <h6>Pages</h6>
                    </div>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="landing-page.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-landing-page"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-landing-page"></use>
                      </svg><span>Landing page</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="sample-page.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-sample-page"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-sample-page"></use>
                      </svg><span>Sample page</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="translate.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-internationalization"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-internationalization"></use>
                      </svg><span>Translate</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="https://admin.pixelstrap.net/zono/starter-kit/index.html" target="_blank">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-starter-kit"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-starter-kit"></use>
                      </svg><span>Starter kit</span></a></li>
                  <li class="mega-menu sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-others"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-others"></use>
                      </svg><span>Others</span></a>
                    <div class="mega-menu-container menu-content">
                      <div class="container-fluid">
                        <div class="row">
                          <div class="col mega-box">
                            <div class="link-section">
                              <div class="submenu-title">
                                <h5>Error Page</h5>
                              </div>
                              <ul class="submenu-content opensubmegamenu">
                                <li><a href="error-400.html">Error 400</a></li>
                                <li><a href="error-401.html">Error 401</a></li>
                                <li><a href="error-403.html">Error 403</a></li>
                                <li><a href="error-404.html">Error 404</a></li>
                                <li><a href="error-500.html">Error 500</a></li>
                                <li><a href="error-503.html">Error 503</a></li>
                              </ul>
                            </div>
                          </div>
                          <div class="col mega-box">
                            <div class="link-section">
                              <div class="submenu-title">
                                <h5> Authentication</h5>
                              </div>
                              <ul class="submenu-content opensubmegamenu">
                                <li><a href="login.html" target="_blank">Login Simple</a></li>
                                <li><a href="login_one.html" target="_blank">Login with bg image</a></li>
                                <li><a href="login_two.html" target="_blank">Login with image two                      </a></li>
                                <li><a href="login-bs-validation.html" target="_blank">Login With validation</a></li>
                                <li><a href="login-bs-tt-validation.html" target="_blank">Login with tooltip</a></li>
                                <li><a href="login-sa-validation.html" target="_blank">Login with sweetalert</a></li>
                                <li><a href="sign-up.html" target="_blank">Register Simple</a></li>
                                <li><a href="sign-up-one.html" target="_blank">Register with Bg Image                              </a></li>
                                <li><a href="sign-up-two.html" target="_blank">Register with image two</a></li>
                                <li><a href="sign-up-wizard.html" target="_blank">Register wizard</a></li>
                                <li><a href="unlock.html">Unlock User</a></li>
                                <li><a href="forget-password.html">Forget Password</a></li>
                                <li><a href="reset-password.html">Reset Password</a></li>
                                <li><a href="maintenance.html">Maintenance</a></li>
                              </ul>
                            </div>
                          </div>
                          <div class="col mega-box">
                            <div class="link-section">
                              <div class="submenu-title">
                                <h5>Coming Soon</h5>
                              </div>
                              <ul class="submenu-content opensubmegamenu">
                                <li><a href="comingsoon.html">Coming Simple</a></li>
                                <li><a href="comingsoon-bg-video.html">Coming with Bg video</a></li>
                                <li><a href="comingsoon-bg-img.html">Coming with Bg Image</a></li>
                              </ul>
                            </div>
                          </div>
                          <div class="col mega-box">
                            <div class="link-section">
                              <div class="submenu-title">
                                <h5>Email templates</h5>
                              </div>
                              <ul class="submenu-content opensubmegamenu">
                                <li><a href="basic-template.html">Basic Email</a></li>
                                <li><a href="email-header.html">Basic With Header</a></li>
                                <li><a href="template-email.html">Ecomerce Template</a></li>
                                <li><a href="template-email-2.html">Email Template 2</a></li>
                                <li><a href="ecommerce-templates.html">Ecommerce Email</a></li>
                                <li><a href="email-order-success.html">Order Success</a></li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                  <li class="sidebar-main-title">
                    <div>
                      <h6>Miscellaneous</h6>
                    </div>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-gallery"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-gallery"></use>
                      </svg><span>Gallery</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="gallery.html">Gallery Grid</a></li>
                      <li><a href="gallery-with-description.html">Gallery Grid Desc</a></li>
                      <li><a href="gallery-masonry.html">Masonry Gallery</a></li>
                      <li><a href="masonry-gallery-with-disc.html">Masonry with Desc</a></li>
                      <li><a href="gallery-hover.html">Hover Effects</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-blog"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-blog"></use>
                      </svg><span>Blog</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="blog.html">Blog Details</a></li>
                      <li><a href="blog-single.html">Blog Single</a></li>
                      <li><a href="add-post.html">Add Post</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="faq.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-faq"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-faq"></use>
                      </svg><span>FAQ</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-job-search"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-job-search"></use>
                      </svg><span>Job Search</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="job-cards-view.html">Cards view</a></li>
                      <li><a href="job-list-view.html">List View</a></li>
                      <li><a href="job-details.html">Job Details</a></li>
                      <li><a href="job-apply.html">Apply</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-learning"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-learning"></use>
                      </svg><span>Learning</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="learning-list-view.html">Learning List</a></li>
                      <li><a href="learning-detailed.html">Detailed Course</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-maps"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-maps"></use>
                      </svg><span>Maps</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="map-js.html">Maps JS</a></li>
                      <li><a href="vector-map.html">Vector Maps</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-editors"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-editors"></use>
                      </svg><span>Editors</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="summernote.html">Summer Note</a></li>
                      <li><a href="ckeditor.html">CK editor</a></li>
                      <li><a href="simple-MDE.html">MDE editor</a></li>
                      <li><a href="ace-code-editor.html">ACE code editor </a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="knowledgebase.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-knowledgebase"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-knowledgebase"></use>
                      </svg><span>Knowledgebase</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="support-ticket.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-support-tickets"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-support-tickets"></use>
                      </svg><span>Support Ticket</span></a></li>
                </ul>
              </div>
              <div class="right-arrow" id="right-arrow"><i data-feather="arrow-right"></i></div>
            </nav>
          </div>
        </div>
        <!-- Page Sidebar Ends-->
        <div class="page-body">
          <div class="container-fluid">
            <div class="page-title">
              <div class="row">
                <div class="col-xl-4 col-sm-7 box-col-3">
                  <h3>API DataTables</h3>
                </div>
                <div class="col-5 d-none d-xl-block">
                  <!-- Page Sub Header Start-->
                  <div class="left-header main-sub-header p-0">
                    <div class="left-menu-header">
                      <ul class="header-left"> 
                        <li class="onhover-dropdown"> <span class="f-w-700">General </span>
                          <ul class="onhover-show-div left-dropdown">
                            <li class="flyout-right"><a href="#">Dashboards</a><i class="fa fa-angle-right"></i>
                              <ul>
                                <li> <a href="index-2.html">Default  </a></li>
                                <li> <a href="dashboard-02.html">Ecommerce</a></li>
                              </ul>
                            </li>
                            <li class="flyout-right"><a href="#">Widgets</a><i class="fa fa-angle-right"></i>
                              <ul>
                                <li><a href="general-widget.html">General</a></li>
                                <li> <a href="chart-widget.html">chart</a></li>
                              </ul>
                            </li>
                            <li class="flyout-right"> <a href="#">Page layout</a><i class="fa fa-angle-right"></i>
                              <ul>
                                <li> <a href="box-layout.html">Boxed </a></li>
                                <li> <a href="layout-rtl.html">RTL</a></li>
                                <li> <a href="layout-dark.html">Dark Layout</a></li>
                                <li> <a href="footer-light.html">footer-light.html</a></li>
                                <li> <a href="footer-dark.html">footer-dark.html</a></li>
                                <li><a href="footer-fixed.html">footer-fixed.html</a></li>
                              </ul>
                            </li>
                          </ul>
                        </li>
                        <li class="onhover-dropdown"><span class="f-w-700">Components</span>
                          <ul class="onhover-show-div left-dropdown">
                            <li class="flyout-right"><a href="#">Ui Kits</a>
                              <ul>
                                <li><a href="typography.html">Typography</a></li>
                                <li><a href="avatars.html">Avatars</a></li>
                                <li><a href="helper-classes.html">helper classes</a></li>
                                <li><a href="grid.html">Grid</a></li>
                                <li><a href="tag-pills.html">Tag & pills</a></li>
                                <li><a href="progress-bar.html">Progress</a></li>
                                <li><a href="modal.html">Modal</a></li>
                                <li><a href="alert.html">Alert</a></li>
                                <li><a href="popover.html">Popover</a></li>
                                <li><a href="tooltip.html">Tooltip</a></li>
                                <li><a href="dropdown.html">Dropdown</a></li>
                                <li><a href="according.html">Accordion</a></li>
                                <li><a href="tab-bootstrap.html">Tabs</a></li>
                                <li><a href="list.html">Lists</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Bonus Ui</a>
                              <ul>
                                <li><a href="scrollable.html">Scrollable</a></li>
                                <li><a href="tree.html">Tree view</a></li>
                                <li><a href="toasts.html">Toasts</a></li>
                                <li><a href="rating.html">Rating</a></li>
                                <li><a href="dropzone.html">dropzone</a></li>
                                <li><a href="tour.html">Tour</a></li>
                                <li><a href="sweet-alert2.html">SweetAlert2</a></li>
                                <li><a href="modal-animated.html">Animated Modal</a></li>
                                <li><a href="owl-carousel.html">Owl Carousel</a></li>
                                <li><a href="ribbons.html">Ribbons</a></li>
                                <li><a href="pagination.html">Pagination</a></li>
                                <li><a href="breadcrumb.html">Breadcrumb</a></li>
                                <li><a href="range-slider.html">Range Slider</a></li>
                                <li><a href="image-cropper.html">Image cropper</a></li>
                                <li><a href="basic-card.html">Basic Card</a></li>
                                <li><a href="creative-card.html">Creative Card</a></li>
                                <li><a href="dragable-card.html">Draggable Card</a></li>
                                <li><a href="timeline-v-1.html">Timeline </a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Animation</a>
                              <ul>
                                <li><a href="animate.html">Animate</a></li>
                                <li><a href="scroll-reval.html">Scroll Reveal</a></li>
                                <li><a href="AOS.html">AOS animation</a></li>
                                <li><a href="tilt.html">Tilt Animation</a></li>
                                <li><a href="wow.html">Wow Animation</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Icons</a>
                              <ul>
                                <li><a href="flag-icon.html">Flag icon</a></li>
                                <li><a href="font-awesome.html">Fontawesome Icon</a></li>
                                <li><a href="ico-icon.html">Ico Icon</a></li>
                                <li><a href="themify-icon.html">Themify Icon</a></li>
                                <li><a href="feather-icon.html">Feather icon</a></li>
                                <li><a href="whether-icon.html">Whether Icon</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Buttons</a>
                              <ul>
                                <li><a href="buttons.html">Default Style</a></li>
                                <li><a href="button-group.html">Button Group</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Charts</a>
                              <ul>
                                <li><a href="echarts.html">Echarts</a></li>
                                <li><a href="chart-apex.html">Apex Chart</a></li>
                                <li><a href="chart-google.html">Google Chart</a></li>
                                <li><a href="chart-sparkline.html">Sparkline chart</a></li>
                                <li><a href="chart-flot.html">Flot Chart </a></li>
                                <li><a href="chart-knob.html">Knob Chart</a></li>
                                <li><a href="chart-morris.html">Morris Chart</a></li>
                                <li><a href="chartjs.html">Chatjs Chart</a></li>
                                <li><a href="chartist.html">Chartist Chart</a></li>
                                <li><a href="chart-peity.html">Peity Chart</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                          </ul>
                        </li>
                        <li class="onhover-dropdown"> <span class="f-w-700">Applications</span>
                          <ul class="onhover-show-div left-dropdown">
                            <li class="flyout-right"><a href="#">Project</a>
                              <ul>
                                <li><a href="projects.html">Project List</a></li>
                                <li><a href="projectcreate.html">Create new</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li><a href="file-manager.html">File manager</a></li>
                            <li><a href="kanban.html">kanban Board </a></li>
                            <li class="flyout-right"> <a href="#">Ecommerce </a>
                              <ul>
                                <li><a href="add-products.html">Add Product</a></li>
                                <li><a href="product.html">Product</a></li>
                                <li><a href="product-page.html">Product page</a></li>
                                <li><a href="list-products.html">Product list</a></li>
                                <li><a href="payment-details.html">Payment Details</a></li>
                                <li><a href="order-history.html">Order History</a></li>
                                <li class="flyout-right"><a class="submenu-title" href="#">Invoices</a>
                                  <ul>
                                    <li><a href="invoice-1.html">Invoice-1</a></li>
                                    <li><a href="invoice-2.html">Invoice-2</a></li>
                                    <li><a href="invoice-3.html">Invoice-3</a></li>
                                    <li><a href="invoice-4.html">Invoice-4</a></li>
                                    <li><a href="invoice-5.html">Invoice-5</a></li>
                                    <li><a href="invoice-template.html">Invoice-6</a></li>
                                  </ul>
                                </li>
                                <li><a href="cart.html">Cart</a></li>
                                <li><a href="list-wish.html">Wishlist</a></li>
                                <li><a href="checkout.html">Checkout</a></li>
                                <li><a href="pricing.html">Pricing  </a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Email</a>
                              <ul>
                                <li><a href="letter-box.html">Email App</a></li>
                                <li><a href="email-compose.html">Email Compose</a></li>
                                <li><a href="letter-box.html">Letter Box</a></li>
                              </ul><i class="fa fa-angle-right"> </i>
                            </li>
                            <li class="flyout-right"> <a href="#">Chat</a>
                              <ul>
                                <li><a href="private-chat.html">Private Chat</a></li>
                                <li><a href="group-chat.html">Group Chat</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">User</a>
                              <ul>
                                <li><a href="user-profile.html">Users Profile</a></li>
                                <li><a href="edit-profile.html">Users Edit</a></li>
                                <li><a href="user-cards.html">Users Cards</a></li>
                              </ul><i class="fa fa-angle-right"> </i>
                            </li>
                            <li><a href="bookmark.html">Bookmarks</a></li>
                            <li><a href="contacts.html">Contacts</a></li>
                            <li><a href="task.html">Task</a></li>
                            <li><a href="calendar-basic.html">Calendar</a></li>
                            <li><a href="social-app.html">Social-app</a></li>
                            <li><a href="to-do.html">To-Do</a></li>
                            <li><a href="search.html">Search Result</a></li>
                          </ul>
                        </li>
                        <li class="onhover-dropdown"><span class="f-w-700">Pages</span>
                          <ul class="onhover-show-div left-dropdown">
                            <li><a href="blog.html">Landing page</a></li>
                            <li><a href="blog-single.html">Sample page</a></li>
                            <li><a href="add-post.html">Starter kit</a></li>
                            <li class="flyout-right"><a href="#">Others </a><i class="fa fa-angle-right"></i>
                              <ul>
                                <li class="flyout-right"><a href="#">Error Page</a>
                                  <ul>
                                    <li><a href="error-400.html">Error 400</a></li>
                                    <li><a href="error-401.html">Error 401</a></li>
                                    <li><a href="error-403.html">Error 403</a></li>
                                    <li><a href="error-404.html">Error 404</a></li>
                                    <li><a href="error-500.html">Error 500</a></li>
                                    <li><a href="error-503.html">Error 503</a></li>
                                  </ul><i class="fa fa-angle-right"> </i>
                                </li>
                                <li class="flyout-right"> <a href="#">Authentication</a>
                                  <ul>
                                    <li><a href="login.html" target="_blank">Login Simple</a></li>
                                    <li><a href="login_one.html" target="_blank">Login with bg image</a></li>
                                    <li><a href="login_two.html" target="_blank">Login with image two                      </a></li>
                                    <li><a href="login-bs-validation.html" target="_blank">Login With validation</a></li>
                                    <li><a href="login-bs-tt-validation.html" target="_blank">Login with tooltip</a></li>
                                    <li><a href="login-sa-validation.html" target="_blank">Login with sweetalert</a></li>
                                    <li><a href="sign-up.html" target="_blank">Register Simple</a></li>
                                    <li><a href="sign-up-one.html" target="_blank">Register with Bg Image                              </a></li>
                                    <li><a href="sign-up-two.html" target="_blank">Register with image two</a></li>
                                    <li><a href="sign-up-wizard.html" target="_blank">Register wizard</a></li>
                                    <li><a href="unlock.html">Unlock User</a></li>
                                    <li><a href="forget-password.html">Forget Password</a></li>
                                    <li><a href="reset-password.html">Reset Password</a></li>
                                    <li><a href="maintenance.html">Maintenance</a></li>
                                  </ul>
                                </li>
                                <li class="flyout-right"> <a href="#">Coming Soon</a>
                                  <ul> 
                                    <li><a href="comingsoon.html">Coming Simple</a></li>
                                    <li><a href="comingsoon-bg-video.html">Coming with Bg video</a></li>
                                    <li><a href="comingsoon-bg-img.html">Coming with Bg Image</a></li>
                                  </ul><i class="fa fa-angle-right"> </i>
                                </li>
                                <li class="flyout-right"><a href="#">Email templates</a>
                                  <ul>
                                    <li><a href="basic-template.html">Basic Email</a></li>
                                    <li><a href="email-header.html">Basic With Header</a></li>
                                    <li><a href="template-email.html">Ecomerce Template</a></li>
                                    <li><a href="template-email-2.html">Email Template 2</a></li>
                                    <li><a href="ecommerce-templates.html">Ecommerce Email</a></li>
                                    <li><a href="email-order-success.html">Order Success</a></li>
                                  </ul><i class="fa fa-angle-right"></i>
                                </li>
                              </ul>
                            </li>
                          </ul>
                        </li>
                        <li class="onhover-dropdown p-0"><span class="f-w-700">Miscellaneous</span>
                          <ul class="onhover-show-div left-dropdown">
                            <li class="flyout-right"><a href="#">Gallery</a>
                              <ul> 
                                <li><a href="gallery.html">Gallery Grid</a></li>
                                <li><a href="gallery-with-description.html">gallery-with-description</a></li>
                                <li><a href="gallery-masonry.html">gallery-masonry</a></li>
                                <li><a href="masonry-gallery-with-disc.html">masonry-gallery-with-disc</a></li>
                                <li><a href="gallery-hover.html">gallery-hover</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Blog</a>
                              <ul>
                                <li><a href="blog.html">blog</a></li>
                                <li><a href="blog-single.html">blog-single</a></li>
                                <li><a href="add-post.html">add-post</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li><a href="FAQ-2.html">FAQ</a></li>
                            <li class="flyout-right"><a href="#">Job Search</a>
                              <ul>
                                <li><a href="job-cards-view.html">job-cards-view</a></li>
                                <li><a href="job-list-view.html">job-list-view</a></li>
                                <li><a href="job-details.html">job-details</a></li>
                                <li><a href="job-apply.html">job-apply</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Job Search</a>
                              <ul> 
                                <li><a href="job-cards-view.html">learning-list</a></li>
                                <li><a href="learning-detailed.html">learning-detailed</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Maps</a>
                              <ul>
                                <li><a href="map-js.html">Map-js</a></li>
                                <li><a href="vector-map.html">Vector Maps</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Editors </a>
                              <ul>
                                <li><a href="summernote.html">Summer Note</a></li>
                                <li><a href="CK%20editor.html">CK editor</a></li>
                                <li><a href="simple-MDE.html">MDE editor</a></li>
                                <li><a href="ace-code-editor.html">ACE code editor </a></li>
                              </ul><i class="fa fa-angle-right"> </i>
                            </li>
                            <li><a href="knowledgebase.html">Knowledgebase </a></li>
                            <li> <a href="support-ticket-2.html">Support Ticket</a></li>
                          </ul>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <!-- Page Sub Header end
                  -->
                </div>
                <div class="col-xl-3 col-sm-5 box-col-4"> 
                  <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index-2.html">
                        <svg class="stroke-icon"> 
                          <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-home"></use>
                        </svg></a></li>
                    <li class="breadcrumb-item">Data Tables</li>
                    <li class="breadcrumb-item active">API DataTables</li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
          <!-- Container-fluid starts-->
          <div class="container-fluid">
            <div class="row">
              <!-- Add rows  Starts-->
              <div class="col-sm-12">
                <div class="card">
                  <div class="card-header pb-0 card-no-border">
                    <h4 class="mb-3">Add rows </h4><span>New rows can be added to a DataTable using the<code class="api" title="DataTables API method">row.add()</code> API method. Simply call the API function with the data for the new row (be it an array or object). Multiple rows
                                                      can be added using the<code class="api" title="DataTables API method">rows.add()</code> method (note
                                                      the plural). Data can likewise be updated with the <code class="api" title="DataTables API method">row().data()</code> and <code class="api" title="DataTables API method">row().remove()</code> methods.</span><span>Note that in order to see the new row in the table you must call the<code class="api" title="DataTables API method">draw()</code> method, which is easily done through the chaining that the DataTables API employs.</span>
                  </div>
                  <div class="card-body">
                    <button class="btn btn-primary mb-3" id="addRow">Add new row</button>
                    <div class="table-responsive custom-scrollbar">
                      <table class="display" id="API-1">
                        <thead>
                          <tr>
                            <th>Column 1</th>
                            <th>Column 2</th>
                            <th>Column 3</th>
                            <th>Column 4</th>
                            <th>Column 5</th>
                          </tr>
                        </thead>
                        <tfoot>
                          <tr>
                            <th>Column 1</th>
                            <th>Column 2</th>
                            <th>Column 3</th>
                            <th>Column 4</th>
                            <th>Column 5</th>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Add rows Ends-->
              <!-- Child rows (show extra / detailed information) Starts-->
              <div class="col-sm-12">
                <div class="card">
                  <div class="card-header pb-0 card-no-border">
                    <h4 class="mb-3">Child rows (show extra / detailed information) </h4><span>The DataTables API has a number of methods for attaching child rows to a parent row in the DataTable. This can be used to show additional information about a row, useful for cases where you wish to convey more information about a row than there is space for in the host table.</span>
                  </div>
                  <div class="card-body">
                    <div class="table-responsive custom-scrollbar">
                      <table class="display" id="API-chield-row">
                        <thead>
                          <tr>
                            <th></th>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Office</th>
                            <th>Salary</th>
                          </tr>
                        </thead>
                        <tfoot>
                          <tr>
                            <th></th>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Office</th>
                            <th>Salary</th>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Child rows (show extra / detailed information) Ends-->
              <!-- Row Selection And Deletion (Single Row) Starts-->
              <div class="col-sm-12">
                <div class="card">
                  <div class="card-header pb-0 card-no-border">
                    <h4 class="mb-3">Row Selection And Deletion (Single Row)</h4><span>
                      It can be useful to provide the user with the option to select rows in a DataTable. This can be done by using a click event to add / remove a class on the
                      table rows. The <code class="api" title="DataTables API method">rows().data()</code>method can then
                                                            be used to get the data for the selected rows. In this case it is simply counting the number of selected rows, but much more complex interactions can easily be
                                                            developed.</span>
                  </div>
                  <div class="card-body">
                    <div class="table-responsive custom-scrollbar">
                      <button class="btn btn-secondary mb-3" id="single-row-delete-btn">Delete Row</button>
                      <table class="display" id="row-select-delete"> 
                        <thead>
                          <tr>
                            <th>Employee Name</th>
                            <th>Job Designation</th>
                            <th>Company Name</th>
                            <th>Invoice No.</th>
                            <th>Credit/Debit</th>
                            <th>Date</th>
                            <th>Priority</th>
                            <th>Budget</th>
                            <th>Action</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td>Tiger Nixon</td>
                            <td>System Architect</td>
                            <td>Tata Co.</td>
                            <td>#AS61</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">1.4%</i></td>
                            <td>2011/04/25</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$320.800,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Garrett Winters</td>
                            <td>Accountant</td>
                            <td>Edinburgh</td>
                            <td>#FG63</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">1.4%</i></td>
                            <td>2011/07/25</td>
                            <td><span class="badge badge-light-danger">Urgent</span></td>
                            <td>$170.750,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Ashton Cox</td>
                            <td>Junior Technical Author</td>
                            <td>Mphasis Ltd</td>
                            <td>#GH66</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">1.4%</i></td>
                            <td>2009/01/12</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$86.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Cedric Kelly</td>
                            <td>Senior Javascript Developer</td>
                            <td>Edinburgh</td>
                            <td>#UH22</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">1.4%</i></td>
                            <td>2012/03/29</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>$433.060,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Airi Satou</td>
                            <td>Accountant</td>
                            <td>Google Inc.</td>
                            <td>#TY33</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.5%</i></td>
                            <td>2008/11/28</td>
                            <td><span class="badge badge-light-danger">Urgent</span></td>
                            <td>$162.700,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Brielle Williamson</td>
                            <td>Integration Specialist</td>
                            <td>Microsoft</td>
                            <td>#TS61</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.5%</i></td>
                            <td>2012/12/02</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>$372.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Herrod Chandler</td>
                            <td>Sales Assistant</td>
                            <td>Google Co.</td>
                            <td>#GF59</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.5%</i></td>
                            <td>2012/08/06</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$137.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Rhona Davidson</td>
                            <td>Integration Specialist</td>
                            <td>Tokyo</td>
                            <td>#FT55</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">1.4%</i></td>
                            <td>2010/10/14</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$327.900,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Colleen Hurst</td>
                            <td>Javascript Developer</td>
                            <td>Google Co.</td>
                            <td>#NB39</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">2.8%</i></td>
                            <td>2009/09/15</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$205.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Sonya Frost</td>
                            <td>Software Engineer</td>
                            <td>Edinburgh</td>
                            <td>#BH23</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.5%</i></td>
                            <td>2008/12/13</td>
                            <td><span class="badge badge-light-danger">Urgent</span></td>
                            <td>$103.600,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Jena Gaines</td>
                            <td>Office Manager</td>
                            <td>Tata Co.</td>
                            <td>#HN30</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">1.4%</i></td>
                            <td>2008/12/19</td>
                            <td><span class="badge badge-light-primary">High</span></td>
                            <td>$90.560,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Quinn Flynn</td>
                            <td>Support Lead</td>
                            <td>Edinburgh</td>
                            <td>#YH22</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.5%</i></td>
                            <td>2013/03/03</td>
                            <td><span class="badge badge-light-danger">Urgent</span></td>
                            <td>$342.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Charde Marshall</td>
                            <td>Regional Director</td>
                            <td>Google Co.</td>
                            <td>#FV36</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">1.4%</i></td>
                            <td>2008/10/16</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>$470.600,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Haley Kennedy</td>
                            <td>Senior Marketing Designer</td>
                            <td>Tata Co.</td>
                            <td>#TF43</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.5%</i></td>
                            <td>2012/12/18</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>$313.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Tatyana Fitzpatrick</td>
                            <td>Regional Director</td>
                            <td>Infosys Ltd.</td>
                            <td>#DF19</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">1.4%</i></td>
                            <td>2010/03/17</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$385.750,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Michael Silva</td>
                            <td>Marketing Designer</td>
                            <td>Infosys Ltd.</td>
                            <td>#HD66</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.5%</i></td>
                            <td>2012/11/27</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$198.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Paul Byrd</td>
                            <td>Chief Financial Officer (CFO)</td>
                            <td>New York</td>
                            <td>#NH64</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">9.8%</i></td>
                            <td>2010/06/09</td>
                            <td><span class="badge badge-light-danger">Urgent</span></td>
                            <td>$725.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Gloria Little</td>
                            <td>Systems Administrator</td>
                            <td>New York</td>
                            <td>#MN59</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.8%</i></td>
                            <td>2009/04/10</td>
                            <td><span class="badge badge-light-danger">Urgent</span></td>
                            <td>$237.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Bradley Greer</td>
                            <td>Software Engineer</td>
                            <td>Tata Co.</td>
                            <td>#Jh31</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">9.8%</i></td>
                            <td>2012/10/13</td>
                            <td><span class="badge badge-light-primary">High</span></td>
                            <td>$132.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Dai Rios</td>
                            <td>Personnel Lead</td>
                            <td>Edinburgh</td>
                            <td>#YT35</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.8%</i></td>
                            <td>2012/09/26</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>$217.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Jenette Caldwell</td>
                            <td>Development Lead</td>
                            <td>New York</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>#FG30</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">1.4%</i></td>
                            <td>2011/09/03</td>
                            <td><span class="badge badge-light-danger">Urgent</span></td>
                            <td>$345.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Yuri Berry</td>
                            <td>Chief Marketing Officer (CMO)</td>
                            <td>New York</td>
                            <td>#VB40</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">5.6%</i></td>
                            <td>2009/06/25</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$675.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Caesar Vance</td>
                            <td>Pre-Sales Support</td>
                            <td>New York</td>
                            <td>#CV21</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.8%</i></td>
                            <td>2011/12/12</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>$106.450,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Doris Wilder</td>
                            <td>Sales Assistant</td>
                            <td>Sidney</td>
                            <td>#BH23</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">5.6%</i></td>
                            <td>2010/09/20</td>
                            <td><span class="badge badge-light-primary">High</span></td>
                            <td>$85.600,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Angelica Ramos</td>
                            <td>Chief Executive Officer (CEO)</td>
                            <td>Tata Co.</td>
                            <td>#VC47</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.8%</i></td>
                            <td>2009/10/09</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>$1.200.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Gavin Joyce</td>
                            <td>Developer</td>
                            <td>Edinburgh</td>
                            <td>#Th32</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">9.8%</i></td>
                            <td>2010/12/22</td>
                            <td><span class="badge badge-light-danger">Urgent</span></td>
                            <td>$92.575,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Jennifer Chang</td>
                            <td>Regional Director</td>
                            <td>Singapore</td>
                            <td>#BN28</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.8%</i></td>
                            <td>2010/11/14</td>
                            <td><span class="badge badge-light-danger">Urgent</span></td>
                            <td>$357.650,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Brenden Wagner</td>
                            <td>Software Engineer</td>
                            <td>Google Co.</td>
                            <td>#CV28</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.8%</i></td>
                            <td>2011/06/07</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$206.850,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Fiona Green</td>
                            <td>Chief Operating Officer (COO)</td>
                            <td>Infosys Ltd.</td>
                            <td>#GF48</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">9.8%</i></td>
                            <td>2010/03/11</td>
                            <td><span class="badge badge-light-primary">High</span></td>
                            <td>$850.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Shou Itou</td>
                            <td>Regional Marketing</td>
                            <td>Tokyo</td>
                            <td>#BF20</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">5.6%</i></td>
                            <td>2011/08/14</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>$163.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Michelle House</td>
                            <td>Integration Specialist</td>
                            <td>Sidney</td>
                            <td>#DF37</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.8%</i></td>
                            <td>2011/06/02</td>
                            <td><span class="badge badge-light-primary">High</span></td>
                            <td>$95.400,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Suki Burks</td>
                            <td>Developer</td>
                            <td>Infosys Ltd.</td>
                            <td>#ER53</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.8%</i></td>
                            <td>2009/10/22</td>
                            <td><span class="badge badge-light-danger">Urgent</span></td>
                            <td>$114.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Prescott Bartlett</td>
                            <td>Technical Author</td>
                            <td>Tata Co.</td>
                            <td>#DF27</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">5.6%</i></td>
                            <td>2011/05/07</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$145.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Gavin Cortez</td>
                            <td>Team Leader</td>
                            <td>Google Co.</td>
                            <td>#SW22</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">5.6%</i></td>
                            <td>2008/10/26</td>
                            <td><span class="badge badge-light-danger">Urgent</span></td>
                            <td>$235.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Martena Mccray</td>
                            <td>Post-Sales support</td>
                            <td>Edinburgh</td>
                            <td>#ED46</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.8%</i></td>
                            <td>2011/03/09</td>
                            <td><span class="badge badge-light-primary">High</span></td>
                            <td>$324.050,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Unity Butler</td>
                            <td>Marketing Designer</td>
                            <td>Infosys Ltd.</td>
                            <td>#ED47</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">9.8%</i></td>
                            <td>2009/12/09</td>
                            <td><span class="badge badge-light-primary">High</span></td>
                            <td>$85.675,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Howard Hatfield</td>
                            <td>Office Manager</td>
                            <td>Google Co.</td>
                            <td>#WS51</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">1.4%</i></td>
                            <td>2008/12/16</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$164.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Hope Fuentes</td>
                            <td>Secretary</td>
                            <td>Infosys Ltd.</td>
                            <td>#RG41</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">5.6%</i></td>
                            <td>2010/02/12</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>$109.850,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Vivian Harrell</td>
                            <td>Financial Controller</td>
                            <td>Infosys Ltd.</td>
                            <td>#TY62</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.8%</i></td>
                            <td>2009/02/14</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$452.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Timothy Mooney</td>
                            <td>Office Manager</td>
                            <td>Tata Co.</td>
                            <td>#GH37</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">9.8%</i></td>
                            <td>2008/12/11</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>$136.200,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Jackson Bradshaw</td>
                            <td>Director</td>
                            <td>New York</td>
                            <td>#YU65</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.8%</i></td>
                            <td>2008/09/26</td>
                            <td><span class="badge badge-light-primary">High</span></td>
                            <td>$645.750,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Olivia Liang</td>
                            <td>Support Engineer</td>
                            <td>Singapore</td>
                            <td>#GH64</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">1.4%</i></td>
                            <td>2011/02/03</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$234.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Bruno Nash</td>
                            <td>Software Engineer</td>
                            <td>Tata Co.</td>
                            <td>#UY38</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">1.4%</i></td>
                            <td>2011/05/03</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$163.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Sakura Yamamoto</td>
                            <td>Support Engineer</td>
                            <td>Tokyo</td>
                            <td>#RT37</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">1.4%</i></td>
                            <td>2009/08/19</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$139.575,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Thor Walton</td>
                            <td>Developer</td>
                            <td>New York</td>
                            <td>#WE61</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">1.4%</i></td>
                            <td>2013/08/11</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>$98.540,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Finn Camacho</td>
                            <td>Support Engineer</td>
                            <td>Google Co.</td>
                            <td>#YU47</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">1.4%</i></td>
                            <td>2009/07/07</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>$87.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Serge Baldwin</td>
                            <td>Data Coordinator</td>
                            <td>Singapore</td>
                            <td>#QW64</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">1.4%</i></td>
                            <td>2012/04/09</td>
                            <td><span class="badge badge-light-primary">High</span></td>
                            <td>$138.575,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Zenaida Frank</td>
                            <td>Software Engineer</td>
                            <td>New York</td>
                            <td>#WE63</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">1.4%</i></td>
                            <td>2010/01/04</td>
                            <td><span class="badge badge-light-primary">High</span></td>
                            <td>$125.250,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Zorita Serrano</td>
                            <td>Software Engineer</td>
                            <td>Google Co.</td>
                            <td>#ER56</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">1.4%</i></td>
                            <td>2012/06/01</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$115.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Jennifer Acosta</td>
                            <td>Junior Javascript Developer</td>
                            <td>Edinburgh</td>
                            <td>#RT43</td>
                            <td> <i class="icofont icofont-arrow-up me-1 text-success">2.8%</i></td>
                            <td>2013/02/01</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$75.650,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Cara Stevens</td>
                            <td>Sales Assistant</td>
                            <td>New York</td>
                            <td>#TY46</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.5%</i></td>
                            <td>2011/12/06</td>
                            <td><span class="badge badge-light-primary">High</span></td>
                            <td>$145.600,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Hermione Butler</td>
                            <td>Regional Director</td>
                            <td>Tata Co.</td>
                            <td>#QA47</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.5%</i></td>
                            <td>2011/03/21</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$356.250,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Lael Greer</td>
                            <td>Systems Administrator</td>
                            <td>Tata Co.</td>
                            <td>#QS21</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.5%</i></td>
                            <td>2009/02/27</td>
                            <td><span class="badge badge-light-warning">Medium</span></td>
                            <td>$103.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Jonas Alexander</td>
                            <td>Developer</td>
                            <td>Infosys Ltd.</td>
                            <td>#ED30</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.5%</i></td>
                            <td>2010/07/14</td>
                            <td><span class="badge badge-light-primary">High</span></td>
                            <td>$86.500,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Shad Decker</td>
                            <td>Regional Director</td>
                            <td>Edinburgh</td>
                            <td>#SD51</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.5%</i></td>
                            <td>2008/11/13</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>$183.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Michael Bruce</td>
                            <td>Javascript Developer</td>
                            <td>Singapore</td>
                            <td>#RF29</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.5%</i></td>
                            <td>2011/06/27</td>
                            <td><span class="badge badge-light-success">Low</span></td>
                            <td>$183.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                          <tr>
                            <td>Donna Snider</td>
                            <td>Customer Support</td>
                            <td>New York</td>
                            <td>#GD27</td>
                            <td> <i class="icofont icofont-arrow-down me-1 text-danger">2.5%</i></td>
                            <td>2011/01/25</td>
                            <td><span class="badge badge-light-primary">High</span></td>
                            <td>$112.000,00</td>
                            <td> 
                              <ul class="action"> 
                                <li class="edit"> <a href="#"><i class="icon-pencil-alt"></i></a></li>
                                <li class="delete"><a href="#"><i class="icon-trash"></i></a></li>
                              </ul>
                            </td>
                          </tr>
                        </tbody>
                        <tfoot>
                          <tr>
                            <th>Employee Name</th>
                            <th>Job Designation</th>
                            <th>Company Name</th>
                            <th>Invoice No.</th>
                            <th>Credit/Debit</th>
                            <th>Date</th>
                            <th>Priority</th>
                            <th>Budget</th>
                            <th>Action</th>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Row Selection And Deletion (Single Row) Ends-->
              <!-- Multiple table control elements  Starts-->
              <div class="col-sm-12">
                <div class="card">
                  <div class="card-header pb-0 card-no-border">
                    <h4 class="mb-3">Custom filtering - range search</h4><span>This example shows a search being performed on the age column in the data, based upon two inputs.</span>
                  </div>
                  <div class="card-body">
                    <div class="table-responsive custom-scrollbar dataTables_wrapper me-0">
                      <table>
                        <tbody class="dataTables_filter">
                          <tr>
                            <td>Minimum age:</td>
                            <td>
                              <input class="form-control" id="min" type="search" name="min">
                            </td>
                          </tr>
                          <tr> 
                            <td>Maximum age:</td>
                            <td>
                              <input class="form-control" id="max" type="search" name="max">
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                    <div class="table-responsive custom-scrollbar user-datatable">
                      <table class="display" id="datatable-range">
                        <thead>
                          <tr>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Office</th>
                            <th>Age</th>
                            <th>Start date</th>
                            <th>Salary</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr>
                            <td> <img class="img-fluid table-avtar" src="../assets/images/user/2.png" alt="">Tiger Nixon</td>
                            <td>System Architect</td>
                            <td>Edinburgh</td>
                            <td>61</td>
                            <td>2011/04/25</td>
                            <td>$320,800</td>
                          </tr>
                          <tr>
                            <td> <img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Garrett Winters</td>
                            <td>Accountant</td>
                            <td>Tokyo</td>
                            <td>63</td>
                            <td>2011/07/25</td>
                            <td>$170,750</td>
                          </tr>
                          <tr>
                            <td> <img class="img-fluid table-avtar" src="../assets/images/user/11.png" alt="">Ashton Cox</td>
                            <td>Junior Technical Author</td>
                            <td>San Francisco</td>
                            <td>66</td>
                            <td>2009/01/12</td>
                            <td>$86,000</td>
                          </tr>
                          <tr>
                            <td> <img class="img-fluid table-avtar" src="../assets/images/user/3.png" alt="">Cedric Kelly</td>
                            <td>Senior Javascript Developer</td>
                            <td>Edinburgh</td>
                            <td>22</td>
                            <td>2012/03/29</td>
                            <td>$433,060</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Airi Satou</td>
                            <td>Accountant</td>
                            <td>Tokyo</td>
                            <td>33</td>
                            <td>2008/11/28</td>
                            <td>$162,700</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Brielle Williamson</td>
                            <td>Integration Specialist</td>
                            <td>New York</td>
                            <td>61</td>
                            <td>2012/12/02</td>
                            <td>$372,000</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Herrod Chandler</td>
                            <td>Sales Assistant</td>
                            <td>San Francisco</td>
                            <td>59</td>
                            <td>2012/08/06</td>
                            <td>$137,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Rhona Davidson</td>
                            <td>Integration Specialist</td>
                            <td>Tokyo</td>
                            <td>55</td>
                            <td>2010/10/14</td>
                            <td>$327,900</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Colleen Hurst</td>
                            <td>Javascript Developer</td>
                            <td>San Francisco</td>
                            <td>39</td>
                            <td>2009/09/15</td>
                            <td>$205,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Sonya Frost</td>
                            <td>Software Engineer</td>
                            <td>Edinburgh</td>
                            <td>23</td>
                            <td>2008/12/13</td>
                            <td>$103,600</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Jena Gaines</td>
                            <td>Office Manager</td>
                            <td>London</td>
                            <td>30</td>
                            <td>2008/12/19</td>
                            <td>$90,560</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Quinn Flynn</td>
                            <td>Support Lead</td>
                            <td>Edinburgh</td>
                            <td>22</td>
                            <td>2013/03/03</td>
                            <td>$342,000</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Charde Marshall</td>
                            <td>Regional Director</td>
                            <td>San Francisco</td>
                            <td>36</td>
                            <td>2008/10/16</td>
                            <td>$470,600</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Haley Kennedy</td>
                            <td>Senior Marketing Designer</td>
                            <td>London</td>
                            <td>43</td>
                            <td>2012/12/18</td>
                            <td>$313,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Tatyana Fitzpatrick</td>
                            <td>Regional Director</td>
                            <td>London</td>
                            <td>19</td>
                            <td>2010/03/17</td>
                            <td>$385,750</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Michael Silva</td>
                            <td>Marketing Designer</td>
                            <td>London</td>
                            <td>66</td>
                            <td>2012/11/27</td>
                            <td>$198,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Paul Byrd</td>
                            <td>Chief Financial Officer (CFO)</td>
                            <td>New York</td>
                            <td>64</td>
                            <td>2010/06/09</td>
                            <td>$725,000</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Gloria Little</td>
                            <td>Systems Administrator</td>
                            <td>New York</td>
                            <td>59</td>
                            <td>2009/04/10</td>
                            <td>$237,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Bradley Greer</td>
                            <td>Software Engineer</td>
                            <td>London</td>
                            <td>41</td>
                            <td>2012/10/13</td>
                            <td>$132,000</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Dai Rios</td>
                            <td>Personnel Lead</td>
                            <td>Edinburgh</td>
                            <td>35</td>
                            <td>2012/09/26</td>
                            <td>$217,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Jenette Caldwell</td>
                            <td>Development Lead</td>
                            <td>New York</td>
                            <td>30</td>
                            <td>2011/09/03</td>
                            <td>$345,000</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Yuri Berry</td>
                            <td>Chief Marketing Officer (CMO)</td>
                            <td>New York</td>
                            <td>40</td>
                            <td>2009/06/25</td>
                            <td>$675,000</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Caesar Vance</td>
                            <td>Pre-Sales Support</td>
                            <td>New York</td>
                            <td>21</td>
                            <td>2011/12/12</td>
                            <td>$106,450</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Doris Wilder</td>
                            <td>Sales Assistant</td>
                            <td>Sidney</td>
                            <td>23</td>
                            <td>2010/09/20</td>
                            <td>$85,600</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Angelica Ramos</td>
                            <td>Chief Executive Officer (CEO)</td>
                            <td>London</td>
                            <td>47</td>
                            <td>2009/10/09</td>
                            <td>$1,200,000</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Gavin Joyce</td>
                            <td>Developer</td>
                            <td>Edinburgh</td>
                            <td>42</td>
                            <td>2010/12/22</td>
                            <td>$92,575</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Jennifer Chang</td>
                            <td>Regional Director</td>
                            <td>Singapore</td>
                            <td>28</td>
                            <td>2010/11/14</td>
                            <td>$357,650</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Brenden Wagner</td>
                            <td>Software Engineer</td>
                            <td>San Francisco</td>
                            <td>28</td>
                            <td>2011/06/07</td>
                            <td>$206,850</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Fiona Green</td>
                            <td>Chief Operating Officer (COO)</td>
                            <td>San Francisco</td>
                            <td>48</td>
                            <td>2010/03/11</td>
                            <td>$850,000</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Shou Itou</td>
                            <td>Regional Marketing</td>
                            <td>Tokyo</td>
                            <td>20</td>
                            <td>2011/08/14</td>
                            <td>$163,000</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Michelle House</td>
                            <td>Integration Specialist</td>
                            <td>Sidney</td>
                            <td>37</td>
                            <td>2011/06/02</td>
                            <td>$95,400</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Suki Burks</td>
                            <td>Developer</td>
                            <td>London</td>
                            <td>53</td>
                            <td>2009/10/22</td>
                            <td>$114,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Prescott Bartlett</td>
                            <td>Technical Author</td>
                            <td>London</td>
                            <td>27</td>
                            <td>2011/05/07</td>
                            <td>$145,000</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Gavin Cortez</td>
                            <td>Team Leader</td>
                            <td>San Francisco</td>
                            <td>22</td>
                            <td>2008/10/26</td>
                            <td>$235,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Martena Mccray</td>
                            <td>Post-Sales support</td>
                            <td>Edinburgh</td>
                            <td>46</td>
                            <td>2011/03/09</td>
                            <td>$324,050</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Unity Butler</td>
                            <td>Marketing Designer</td>
                            <td>San Francisco</td>
                            <td>47</td>
                            <td>2009/12/09</td>
                            <td>$85,675</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Howard Hatfield</td>
                            <td>Office Manager</td>
                            <td>San Francisco</td>
                            <td>51</td>
                            <td>2008/12/16</td>
                            <td>$164,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Hope Fuentes</td>
                            <td>Secretary</td>
                            <td>San Francisco</td>
                            <td>41</td>
                            <td>2010/02/12</td>
                            <td>$109,850</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Vivian Harrell</td>
                            <td>Financial Controller</td>
                            <td>San Francisco</td>
                            <td>62</td>
                            <td>2009/02/14</td>
                            <td>$452,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Timothy Mooney</td>
                            <td>Office Manager</td>
                            <td>London</td>
                            <td>37</td>
                            <td>2008/12/11</td>
                            <td>$136,200</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Jackson Bradshaw</td>
                            <td>Director</td>
                            <td>New York</td>
                            <td>65</td>
                            <td>2008/09/26</td>
                            <td>$645,750</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Olivia Liang</td>
                            <td>Support Engineer</td>
                            <td>Singapore</td>
                            <td>64</td>
                            <td>2011/02/03</td>
                            <td>$234,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Bruno Nash</td>
                            <td>Software Engineer</td>
                            <td>London</td>
                            <td>38</td>
                            <td>2011/05/03</td>
                            <td>$163,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Sakura Yamamoto</td>
                            <td>Support Engineer</td>
                            <td>Tokyo</td>
                            <td>37</td>
                            <td>2009/08/19</td>
                            <td>$139,575</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Thor Walton</td>
                            <td>Developer</td>
                            <td>New York</td>
                            <td>61</td>
                            <td>2013/08/11</td>
                            <td>$98,540</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Finn Camacho</td>
                            <td>Support Engineer</td>
                            <td>San Francisco</td>
                            <td>47</td>
                            <td>2009/07/07</td>
                            <td>$87,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Serge Baldwin</td>
                            <td>Data Coordinator</td>
                            <td>Singapore</td>
                            <td>64</td>
                            <td>2012/04/09</td>
                            <td>$138,575</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Zenaida Frank</td>
                            <td>Software Engineer</td>
                            <td>New York</td>
                            <td>63</td>
                            <td>2010/01/04</td>
                            <td>$125,250</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Zorita Serrano</td>
                            <td>Software Engineer</td>
                            <td>San Francisco</td>
                            <td>56</td>
                            <td>2012/06/01</td>
                            <td>$115,000</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Jennifer Acosta</td>
                            <td>Junior Javascript Developer</td>
                            <td>Edinburgh</td>
                            <td>43</td>
                            <td>2013/02/01</td>
                            <td>$75,650</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Cara Stevens</td>
                            <td>Sales Assistant</td>
                            <td>New York</td>
                            <td>46</td>
                            <td>2011/12/06</td>
                            <td>$145,600</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Hermione Butler</td>
                            <td>Regional Director</td>
                            <td>London</td>
                            <td>47</td>
                            <td>2011/03/21</td>
                            <td>$356,250</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Lael Greer</td>
                            <td>Systems Administrator</td>
                            <td>London</td>
                            <td>21</td>
                            <td>2009/02/27</td>
                            <td>$103,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Jonas Alexander</td>
                            <td>Developer</td>
                            <td>San Francisco</td>
                            <td>30</td>
                            <td>2010/07/14</td>
                            <td>$86,500</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Shad Decker</td>
                            <td>Regional Director</td>
                            <td>Edinburgh</td>
                            <td>51</td>
                            <td>2008/11/13</td>
                            <td>$183,000</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Michael Bruce</td>
                            <td>Javascript Developer</td>
                            <td>Singapore</td>
                            <td>29</td>
                            <td>2011/06/27</td>
                            <td>$183,000</td>
                          </tr>
                          <tr>
                            <td><img class="img-fluid table-avtar" src="../assets/images/user/7.jpg" alt="">Donna Snider</td>
                            <td>Customer Support</td>
                            <td>New York</td>
                            <td>27</td>
                            <td>2011/01/25</td>
                            <td>$112,000</td>
                          </tr>
                        </tbody>
                        <tfoot>
                          <tr>
                            <th>Name</th>
                            <th>Position</th>
                            <th>Office</th>
                            <th>Age</th>
                            <th>Start date</th>
                            <th>Salary</th>
                          </tr>
                        </tfoot>
                      </table>
                    </div>
                  </div>
                </div>
              </div>
              <!-- Multiple table control elements Ends-->
            </div>
          </div>
          <!-- Container-fluid Ends-->
        </div>
        <!-- footer start-->
        <footer class="footer">
          <div class="container-fluid">
            <div class="row">
              <div class="col-md-6 p-0 footer-copyright">
                <p class="mb-0">Copyright 2024 © Zono theme by pixelstrap.</p>
              </div>
              <div class="col-md-6 p-0">
                <p class="heart mb-0">Hand crafted &amp; made with
                  <svg class="footer-icon">
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#heart"></use>
                  </svg>
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
    <!-- latest jquery-->
    <script src="../assets/js/jquery.min.js"></script>
    <!-- Bootstrap js-->
    <script src="../assets/js/bootstrap/bootstrap.bundle.min.js"></script>
    <!-- feather icon js-->
    <script src="../assets/js/icons/feather-icon/feather.min.js"></script>
    <script src="../assets/js/icons/feather-icon/feather-icon.js"></script>
    <!-- scrollbar js-->
    <script src="../assets/js/scrollbar/simplebar.js"></script>
    <script src="../assets/js/scrollbar/custom.js"></script>
    <!-- Sidebar jquery-->
    <script src="../assets/js/config.js"></script>
    <!-- Plugins JS start-->
    <script src="../assets/js/sidebar-menu.js"></script>
    <script src="../assets/js/sidebar-pin.js"></script>
    <script src="../assets/js/slick/slick.min.js"></script>
    <script src="../assets/js/slick/slick.js"></script>
    <script src="../assets/js/header-slick.js"></script>
    <script src="../assets/js/datatable/datatables/jquery.dataTables.min.js"></script>
    <script src="../assets/js/datatable/datatables/datatable.custom.js"></script>
    <!-- Plugins JS Ends-->
    <!-- Theme js-->
    <script src="../assets/js/script.js"></script>
    <script src="../assets/js/theme-customizer/customizer.js"></script>
    <!-- Plugin used-->
  </body>

<!-- Mirrored from admin.pixelstrap.net/zono/template/datatable-API.html by HTTrack Website Copier/3.x [XR&CO'2014], Thu, 17 Jul 2025 13:40:32 GMT -->
</html>