<!DOCTYPE html>
<html lang="en">
  
<!-- Mirrored from admin.pixelstrap.net/zono/template/form-wizard.html by HTTrack Website Copier/3.x [XR&CO'2014], Thu, 17 Jul 2025 13:40:11 GMT -->
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="Zono admin is super flexible, powerful, clean &amp; modern responsive bootstrap 5 admin template with unlimited possibilities.">
    <meta name="keywords" content="admin template, Zono admin template, dashboard template, flat admin template, responsive admin template, web app">
    <meta name="author" content="pixelstrap">
    <link rel="icon" href="../assets/images/favicon.png" type="image/x-icon">
    <link rel="shortcut icon" href="../assets/images/favicon.png" type="image/x-icon">
    <title>Zono - Premium Admin Template</title>
    <!-- Google font -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin="">
    <link href="https://fonts.googleapis.com/css2?family=Nunito+Sans:wght@200;300;400;600;700;800;900&amp;display=swap" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css?family=Roboto:300,300i,400,400i,500,500i,700,700i,900&amp;display=swap" rel="stylesheet">
    <link rel="stylesheet" type="text/css" href="../assets/css/font-awesome.css">
    <!-- ico-font-->
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/icofont.css">
    <!-- Themify icon-->
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/themify.css">
    <!-- Flag icon-->
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/flag-icon.css">
    <!-- Feather icon-->
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/feather-icon.css">
    <!-- Plugins css start-->
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/slick.css">
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/slick-theme.css">
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/scrollbar.css">
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/animate.css">
    <!-- Plugins css Ends-->
    <!-- Bootstrap css-->
    <link rel="stylesheet" type="text/css" href="../assets/css/vendors/bootstrap.css">
    <!-- App css-->
    <link rel="stylesheet" type="text/css" href="../assets/css/style.css">
    <link id="color" rel="stylesheet" href="../assets/css/color-1.css" media="screen">
    <!-- Responsive css-->
    <link rel="stylesheet" type="text/css" href="../assets/css/responsive.css">
  </head>
  <body> 
    <!-- loader starts-->
    <div class="loader-wrapper">
      <div class="theme-loader">    
        <div class="loader-p"></div>
      </div>
    </div>
    <!-- loader ends-->
    <!-- tap on top starts-->
    <div class="tap-top"><i data-feather="chevrons-up"></i></div>
    <!-- tap on tap ends-->
    <!-- page-wrapper Start-->
    <div class="page-wrapper compact-wrapper" id="pageWrapper">
      <!-- Page Header Start-->
      <div class="page-header">
        <div class="header-wrapper row m-0">
          <div class="header-logo-wrapper col-auto p-0">
            <div class="logo-wrapper"><a href="index-2.html"> <img class="img-fluid for-light" src="../assets/images/logo/logo.png" alt=""><img class="img-fluid for-dark" src="../assets/images/logo/logo_dark.png" alt=""></a></div>
            <div class="toggle-sidebar">
              <svg class="sidebar-toggle"> 
                <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-animation"></use>
              </svg>
            </div>
          </div>
          <form class="col-sm-4 form-inline search-full d-none d-xl-block" action="#" method="get">
            <div class="form-group">
              <div class="Typeahead Typeahead--twitterUsers">
                <div class="u-posRelative">
                  <input class="demo-input Typeahead-input form-control-plaintext w-100" type="text" placeholder="Type to Search .." name="q" title="" autofocus>
                  <svg class="search-bg svg-color">
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#search"></use>
                  </svg>
                </div>
              </div>
            </div>
          </form>
          <div class="nav-right col-xl-8 col-lg-12 col-auto pull-right right-header p-0">
            <ul class="nav-menus">
              <li class="serchinput">
                <div class="serchbox">
                  <svg>
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#search"></use>
                  </svg>
                </div>
                <div class="form-group search-form">
                  <input type="text" placeholder="Search here...">
                </div>
              </li>
              <li class="onhover-dropdown"> 
                <div class="notification-box">
                  <svg> 
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#Bell"></use>
                  </svg>
                </div>
                <div class="onhover-show-div notification-dropdown"> 
                  <h6 class="f-18 mb-0 dropdown-title">Notifications</h6>
                  <div class="notification-card">
                    <ul>
                      <li>
                        <div class="user-notification">
                          <div><img src="../assets/images/avtar/2.jpg" alt="avatar"></div>
                          <div class="user-description"><a href="letter-box.html">
                              <h4>You have new finical page design.</h4></a><span>Today 11:45pm</span></div>
                        </div>
                        <div class="notification-btn">
                          <button class="btn btn-pill btn-primary" type="button" title="btn btn-pill btn-primary">Accpet</button>
                          <button class="btn btn-pill btn-secondary" type="button" title="btn btn-pill btn-primary">Decline</button>
                        </div>
                        <div class="show-btn"><a href="index-2.html"> <span>Show</span></a></div>
                      </li>
                      <li>
                        <div class="user-notification">
                          <div><img src="../assets/images/avtar/17.jpg" alt="avatar"></div>
                          <div class="user-description"><a href="letter-box.html">
                              <h4>Congrats! you all task for today.</h4></a><span>Today 01:05pm</span></div>
                        </div>
                        <div class="notification-btn">
                          <button class="btn btn-pill btn-primary" type="button" title="btn btn-pill btn-primary">Accpet</button>
                          <button class="btn btn-pill btn-secondary" type="button" title="btn btn-pill btn-primary">Decline</button>
                        </div>
                        <div class="show-btn"><a href="index-2.html"> <span>Show</span></a></div>
                      </li>
                      <li> 
                        <div class="user-notification">
                          <div> <img src="../assets/images/avtar/18.jpg" alt="avatar"></div>
                          <div class="user-description"><a href="letter-box.html">
                              <h4>You have new in landing page design.</h4></a><span>Today 06:55pm</span></div>
                        </div>
                        <div class="notification-btn">
                          <button class="btn btn-pill btn-primary" type="button" title="btn btn-pill btn-primary">Accpet</button>
                          <button class="btn btn-pill btn-secondary" type="button" title="btn btn-pill btn-primary">Decline</button>
                        </div>
                        <div class="show-btn"><a href="index-2.html"> <span>Show</span></a></div>
                      </li>
                      <li>
                        <div class="user-notification">
                          <div><img src="../assets/images/avtar/19.jpg" alt="avatar"></div>
                          <div class="user-description"><a href="letter-box.html">
                              <h4>Congrats! you all task for today.</h4></a><span>Today 06:55pm</span></div>
                        </div>
                        <div class="notification-btn">
                          <button class="btn btn-pill btn-primary" type="button" title="btn btn-pill btn-primary">Accpet</button>
                          <button class="btn btn-pill btn-secondary" type="button" title="btn btn-pill btn-primary">Decline</button>
                        </div>
                        <div class="show-btn"> <a href="index-2.html"> <span>Show</span></a></div>
                      </li>
                      <li> <a class="f-w-700" href="letter-box.html">Check all </a></li>
                    </ul>
                  </div>
                </div>
              </li>
              <li class="onhover-dropdown">
                <svg>
                  <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#Bookmark"></use>
                </svg>
                <div class="onhover-show-div bookmark-flip">
                  <div class="flip-card">
                    <div class="flip-card-inner">
                      <div class="front">
                        <h6 class="f-18 mb-0 dropdown-title">Bookmark</h6>
                        <ul class="bookmark-dropdown">
                          <li>
                            <div class="row">
                              <div class="col-4 text-center"><a href="form-validation.html">
                                  <div class="bookmark-content">
                                    <div class="bookmark-icon bg-light-primary"><i data-feather="file-text"></i></div><span>Forms</span>
                                  </div></a></div>
                              <div class="col-4 text-center"><a href="user-profile.html">
                                  <div class="bookmark-content"> 
                                    <div class="bookmark-icon bg-light-secondary"><i data-feather="user"></i></div><span>Profile</span>
                                  </div></a></div>
                              <div class="col-4 text-center"><a href="bootstrap-basic-table.html">
                                  <div class="bookmark-content">
                                    <div class="bookmark-icon bg-light-warning"> <i data-feather="server"> </i></div><span>Tables </span>
                                  </div></a></div>
                            </div>
                          </li>
                          <li class="text-centermedia-body"> <a class="flip-btn f-w-700" id="flip-btn" href="javascript:void(0)">Add New Bookmark</a></li>
                        </ul>
                      </div>
                      <div class="back">
                        <ul>
                          <li>
                            <div class="bookmark-dropdown flip-back-content">
                              <input type="text" placeholder="search...">
                            </div>
                          </li>
                          <li><a class="f-w-700 d-block flip-back" id="flip-back" href="javascript:void(0)">Back</a></li>
                        </ul>
                      </div>
                    </div>
                  </div>
                </div>
              </li>
              <li class="onhover-dropdown"> 
                <div class="message position-relative">
                  <svg>
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#Message"></use>
                  </svg><span class="rounded-pill badge-danger"></span>
                </div>
                <div class="onhover-show-div message-dropdown">
                  <h6 class="f-18 mb-0 dropdown-title">Message                               </h6>
                  <ul>
                    <li>
                      <div class="d-flex align-items-start">
                        <div class="message-img bg-light-primary"><img src="../assets/images/user/3.jpg" alt=""></div>
                        <div class="flex-grow-1">
                          <h5><a href="letter-box.html">Emay Walter</a></h5>
                          <p>Do you want to go see movie?</p>
                        </div>
                        <div class="notification-right"><i data-feather="x"></i></div>
                      </div>
                    </li>
                    <li>
                      <div class="d-flex align-items-start">
                        <div class="message-img bg-light-primary"><img src="../assets/images/user/6.jpg" alt=""></div>
                        <div class="flex-grow-1">
                          <h5> <a href="letter-box.html">Jason Borne</a></h5>
                          <p>Thank you for rating us.</p>
                        </div>
                        <div class="notification-right"><i data-feather="x"></i></div>
                      </div>
                    </li>
                    <li>
                      <div class="d-flex align-items-start"> 
                        <div class="message-img bg-light-primary"><img src="../assets/images/user/10.jpg" alt=""></div>
                        <div class="flex-grow-1">
                          <h5> <a href="letter-box.html">Sarah Loren</a></h5>
                          <p>What`s the project report update?</p>
                        </div>
                        <div class="notification-right"><i data-feather="x"></i></div>
                      </div>
                    </li>
                    <li> <a class="f-w-700" href="private-chat.html">Check all</a></li>
                  </ul>
                </div>
              </li>
              <li class="cart-nav onhover-dropdown">
                <div class="cart-box"> 
                  <svg>
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#Buy"></use>
                  </svg>
                </div>
                <div class="cart-dropdown onhover-show-div">
                  <h6 class="f-18 mb-0 dropdown-title">Cart</h6>
                  <ul>
                    <li>
                      <div class="d-flex"><img class="img-fluid b-r-5 img-50" src="../assets/images/ecommerce/05.jpg" alt="">
                        <div class="flex-grow-1"> <span>Women's Track Suit</span>
                          <h6 class="font-primary">8 x $65.00</h6>
                        </div>
                        <div class="close-circle"><a class="bg-primary" href="#"><i data-feather="x"></i></a></div>
                      </div>
                    </li>
                    <li>
                      <div class="d-flex"><img class="img-fluid b-r-5 img-50" src="../assets/images/ecommerce/02.jpg" alt="">
                        <div class="flex-grow-1"><span>Men's Jacket</span>
                          <h6 class="font-primary">10 x $50.00</h6>
                        </div>
                        <div class="close-circle"><a class="bg-primary" href="#"><i data-feather="x"></i></a></div>
                      </div>
                    </li>
                    <li class="total">
                      <h6 class="mb-0">Order Total :<span class="f-right">$1020.00</span></h6>
                    </li>
                    <li class="text-center"> <a href="cart.html">
                        <button class="btn btn-outline-primary" type="button">View Cart</button></a><a class="btn btn-primary view-checkout" href="checkout.html">Checkout  </a></li>
                  </ul>
                </div>
              </li>
              <li>
                <div class="mode">
                  <svg class="for-dark">
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#moon"></use>
                  </svg>
                  <svg class="for-light">
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#Sun"></use>
                  </svg>
                </div>
              </li>
              <li class="language-nav">
                <div class="translate_wrapper">
                  <div class="current_lang">
                    <div class="lang"><i class="flag-icon flag-icon-gb"></i><span class="lang-txt box-col-none">EN            </span></div>
                  </div>
                  <div class="more_lang">
                    <div class="lang selected" data-value="en"><i class="flag-icon flag-icon-us"></i><span class="lang-txt">English<span> (US)</span></span></div>
                    <div class="lang" data-value="de"><i class="flag-icon flag-icon-de"></i><span class="lang-txt">Deutsch</span></div>
                    <div class="lang" data-value="es"><i class="flag-icon flag-icon-es"></i><span class="lang-txt">Español</span></div>
                    <div class="lang" data-value="fr"><i class="flag-icon flag-icon-fr"></i><span class="lang-txt">Français</span></div>
                    <div class="lang" data-value="pt"><i class="flag-icon flag-icon-pt"></i><span class="lang-txt">Português<span> (BR)</span></span></div>
                    <div class="lang" data-value="cn"><i class="flag-icon flag-icon-cn"></i><span class="lang-txt">简体中文</span></div>
                    <div class="lang" data-value="ae"><i class="flag-icon flag-icon-ae"></i><span class="lang-txt">لعربية <span> (ae)</span></span></div>
                  </div>
                </div>
              </li>
              <li class="profile-nav onhover-dropdown pe-0 py-0">
                <div class="d-flex align-items-center profile-media"><img class="b-r-25" src="../assets/images/dashboard/profile.png" alt="">
                  <div class="flex-grow-1 user"><span>Helen Walter</span>
                    <p class="mb-0 font-nunito">Admin 
                      <svg>
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#header-arrow-down"></use>
                      </svg>
                    </p>
                  </div>
                </div>
                <ul class="profile-dropdown onhover-show-div">
                  <li><a href="user-profile.html"><i data-feather="user"></i><span>Account </span></a></li>
                  <li><a href="letter-box.html"><i data-feather="mail"></i><span>Inbox</span></a></li>
                  <li><a href="task.html"><i data-feather="file-text"></i><span>Taskboard</span></a></li>
                  <li><a href="edit-profile.html"><i data-feather="settings"></i><span>Settings</span></a></li>
                  <li><a href="login.html"> <i data-feather="log-in"></i><span>Log Out</span></a></li>
                </ul>
              </li>
            </ul>
          </div>
          <script class="result-template" type="text/x-handlebars-template">
            <div class="ProfileCard u-cf">              
            <div class="ProfileCard-avatar"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-airplay m-0"><path d="M5 17H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v10a2 2 0 0 1-2 2h-1"></path><polygon points="12 15 17 21 7 21 12 15"></polygon></svg></div>
            <div class="ProfileCard-details">
            <div class="ProfileCard-realName">{{name}}</div>
            </div>
            </div>
          </script>
          <script class="empty-template" type="text/x-handlebars-template"><div class="EmptyMessage">Your search turned up 0 results. This most likely means the backend is down, yikes!</div></script>
        </div>
      </div>
      <!-- Page Header Ends                              -->
      <!-- Page Body Start-->
      <div class="page-body-wrapper">
        <!-- Page Sidebar Start-->
        <div class="sidebar-wrapper" data-layout="stroke-svg">
          <div>
            <div class="logo-wrapper"><a href="index-2.html"> <img class="img-fluid for-light" src="../assets/images/logo/logo.png" alt=""><img class="img-fluid for-dark" src="../assets/images/logo/logo_dark.png" alt=""></a>
              <div class="toggle-sidebar">
                <svg class="sidebar-toggle"> 
                  <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#toggle-icon"></use>
                </svg>
              </div>
            </div>
            <div class="logo-icon-wrapper"><a href="index-2.html"><img class="img-fluid" src="../assets/images/logo/logo-icon.png" alt=""></a></div>
            <nav class="sidebar-main">
              <div class="left-arrow" id="left-arrow"><i data-feather="arrow-left"></i></div>
              <div id="sidebar-menu">
                <ul class="sidebar-links" id="simple-bar">
                  <li class="back-btn"><a href="index-2.html"><img class="img-fluid" src="../assets/images/logo/logo-icon.png" alt=""></a>
                    <div class="mobile-back text-end"><span>Back</span><i class="fa fa-angle-right ps-2" aria-hidden="true"></i></div>
                  </li>
                  <li class="pin-title sidebar-main-title">
                    <div> 
                      <h6>Pinned</h6>
                    </div>
                  </li>
                  <li class="sidebar-main-title">
                    <div>
                      <h6 class="lan-1">General</h6>
                    </div>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-home"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-home"></use>
                      </svg><span class="lan-3">Dashboard          </span></a>
                    <ul class="sidebar-submenu">
                      <li><a class="lan-4" href="index-2.html">Default</a></li>
                      <li><a class="lan-5" href="dashboard-02.html">Ecommerce</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-widget"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-widget"></use>
                      </svg><span class="lan-6">Widgets</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="general-widget.html">General</a></li>
                      <li> <a href="chart-widget.html">Chart</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"> <i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-layout"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-layout"></use>
                      </svg><span class="lan-7">Page layout</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="box-layout.html">Boxed</a></li>
                      <li><a href="layout-rtl.html">RTL</a></li>
                      <li><a href="layout-dark.html">Dark Layout</a></li>
                      <li><a href="hide-on-scroll.html">Hide Nav Scroll</a></li>
                      <li><a href="footer-light.html">Footer Light</a></li>
                      <li><a href="footer-dark.html">Footer Dark</a></li>
                      <li><a href="footer-fixed.html">Footer Fixed</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-main-title">
                    <div>
                      <h6 class="lan-8">Applications</h6>
                    </div>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack">    </i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-project"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-project"></use>
                      </svg><span>Project           </span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="projects.html">Project List</a></li>
                      <li><a href="projectcreate.html">Create new</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="file-manager.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-file"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-file"></use>
                      </svg><span>File manager</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack">        </i><a class="sidebar-link sidebar-title link-nav" href="kanban.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-board"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-board"></use>
                      </svg><span>kanban Board</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-ecommerce"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-ecommerce"></use>
                      </svg><span>Ecommerce</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="add-products.html">Add Product</a></li>
                      <li><a href="product.html">Product</a></li>
                      <li><a href="product-page.html">Product page</a></li>
                      <li><a href="list-products.html">Product list</a></li>
                      <li><a href="payment-details.html">Payment Details</a></li>
                      <li><a href="order-history.html">Order History</a></li>
                      <li><a class="submenu-title" href="#">Invoices
                          <h5 class="sub-arrow"><i class="fa fa-angle-right"></i></h5></a>
                        <ul class="submenu-content opensubmegamenu">
                          <li><a href="invoice-1.html">Invoice-1</a></li>
                          <li><a href="invoice-2.html">Invoice-2</a></li>
                          <li><a href="invoice-3.html">Invoice-3</a></li>
                          <li><a href="invoice-4.html">Invoice-4</a></li>
                          <li><a href="invoice-5.html">Invoice-5</a></li>
                          <li><a href="invoice-template.html">Invoice-6</a></li>
                        </ul>
                      </li>
                      <li><a href="cart.html">Cart</a></li>
                      <li><a href="list-wish.html">Wishlist</a></li>
                      <li><a href="checkout.html">Checkout</a></li>
                      <li><a href="pricing.html">Pricing          </a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"> </i><a class="sidebar-link sidebar-title link-nav" href="letter-box.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-email"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-email"></use>
                      </svg><span>Letter Box</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#"> 
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-chat"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-chat"></use>
                      </svg><span>Chat</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="private-chat.html">Private Chat</a></li>
                      <li><a href="group-chat.html">Group Chat</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-user"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-user"></use>
                      </svg><span>Users</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="user-profile.html">Users Profile</a></li>
                      <li><a href="edit-profile.html">Users Edit</a></li>
                      <li><a href="user-cards.html">Users Cards</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="bookmark.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-bookmark"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-bookmark"> </use>
                      </svg><span>Bookmarks </span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="contacts.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-contact"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-contact"> </use>
                      </svg><span>Contacts</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="task.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-task"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-task"> </use>
                      </svg><span>Tasks</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="calendar-basic.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-calendar"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-calender"></use>
                      </svg><span>Calendar</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="social-app.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-social"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-social"> </use>
                      </svg><span>Social App</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="to-do.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-to-do"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-to-do"> </use>
                      </svg><span>To-Do</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="search.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-search"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-search"> </use>
                      </svg><span>Search Result</span></a></li>
                  <li class="sidebar-main-title">
                    <div>
                      <h6>Forms & Table</h6>
                    </div>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-form"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-form"> </use>
                      </svg><span>Forms</span></a>
                    <ul class="sidebar-submenu">
                      <li><a class="submenu-title" href="#">Form Controls
                          <h5 class="sub-arrow"><i class="fa fa-angle-right"></i></h5></a>
                        <ul class="submenu-content opensubmegamenu">
                          <li><a href="form-validation.html">Form Validation</a></li>
                          <li><a href="base-input.html">Base Inputs</a></li>
                          <li><a href="radio-checkbox-control.html">Checkbox & Radio</a></li>
                          <li><a href="input-group.html">Input Groups</a></li>
                          <li> <a href="input-mask.html">Input Mask</a></li>
                          <li><a href="megaoptions.html">Mega Options</a></li>
                        </ul>
                      </li>
                      <li><a class="submenu-title" href="#">Form Widgets
                          <h5 class="sub-arrow"><i class="fa fa-angle-right"></i></h5></a>
                        <ul class="submenu-content opensubmegamenu">
                          <li><a href="datepicker.html">Datepicker</a></li>
                          <li><a href="touchspin.html">Touchspin</a></li>
                          <li><a href="select2.html">Select2</a></li>
                          <li><a href="switch.html">Switch</a></li>
                          <li><a href="typeahead.html">Typeahead</a></li>
                          <li><a href="clipboard.html">Clipboard</a></li>
                        </ul>
                      </li>
                      <li><a class="submenu-title" href="#">Form layout
                          <h5 class="sub-arrow"><i class="fa fa-angle-right"></i></h5></a>
                        <ul class="submenu-content opensubmegamenu">
                          <li><a href="form-wizard.html">Form Wizard 1</a></li>
                          <li><a href="form-wizard-two.html">Form Wizard 2</a></li>
                          <li><a href="two-factor.html">Two Factor</a></li>
                        </ul>
                      </li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-table"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-table"></use>
                      </svg><span>Tables</span></a>
                    <ul class="sidebar-submenu">
                      <li><a class="submenu-title" href="#">Bootstrap Tables
                          <h5 class="sub-arrow"><i class="fa fa-angle-right"></i></h5></a>
                        <ul class="submenu-content opensubmegamenu">
                          <li><a href="bootstrap-basic-table.html">Basic Tables</a></li>
                          <li><a href="table-components.html">Table components</a></li>
                        </ul>
                      </li>
                      <li><a class="submenu-title" href="#">Data Tables
                          <h5 class="sub-arrow"><i class="fa fa-angle-right"></i></h5></a>
                        <ul class="submenu-content opensubmegamenu">
                          <li><a href="datatable-basic-init.html">Basic Init</a></li>
                          <li> <a href="datatable-advance.html">Advance Init </a></li>
                          <li><a href="datatable-API.html">API</a></li>
                          <li><a href="datatable-data-source.html">Data Sources</a></li>
                        </ul>
                      </li>
                      <li><a href="datatable-ext-autofill.html">Ex. Data Tables</a></li>
                      <li><a href="jsgrid-table.html">Js Grid Table        </a></li>
                    </ul>
                  </li>
                  <li class="sidebar-main-title">
                    <div>
                      <h6>Components</h6>
                    </div>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-ui-kits"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-ui-kits"></use>
                      </svg><span>Ui Kits</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="typography.html">Typography</a></li>
                      <li><a href="avatars.html">Avatars</a></li>
                      <li><a href="helper-classes.html">helper classes</a></li>
                      <li><a href="grid.html">Grid</a></li>
                      <li><a href="tag-pills.html">Tag & pills</a></li>
                      <li><a href="progress-bar.html">Progress</a></li>
                      <li><a href="modal.html">Modal</a></li>
                      <li><a href="alert.html">Alert</a></li>
                      <li><a href="popover.html">Popover</a></li>
                      <li><a href="tooltip.html">Tooltip</a></li>
                      <li><a href="dropdown.html">Dropdown</a></li>
                      <li><a href="according.html">Accordion</a></li>
                      <li><a href="tab-bootstrap.html">Tabs</a></li>
                      <li><a href="list.html">Lists</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-bonus-kit"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-bonus-kit"></use>
                      </svg><span>Bonus Ui</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="scrollable.html">Scrollable</a></li>
                      <li><a href="tree.html">Tree view</a></li>
                      <li><a href="toasts.html">Toasts</a></li>
                      <li><a href="rating.html">Rating</a></li>
                      <li><a href="dropzone.html">dropzone</a></li>
                      <li><a href="tour.html">Tour</a></li>
                      <li><a href="sweet-alert2.html">SweetAlert2</a></li>
                      <li><a href="modal-animated.html">Animated Modal</a></li>
                      <li><a href="owl-carousel.html">Owl Carousel</a></li>
                      <li><a href="ribbons.html">Ribbons</a></li>
                      <li><a href="pagination.html">Pagination</a></li>
                      <li><a href="breadcrumb.html">Breadcrumb</a></li>
                      <li><a href="range-slider.html">Range Slider</a></li>
                      <li><a href="image-cropper.html">Image cropper</a></li>
                      <li><a href="basic-card.html">Basic Card</a></li>
                      <li><a href="creative-card.html">Creative Card</a></li>
                      <li><a href="dragable-card.html">Draggable Card</a></li>
                      <li><a href="timeline-v-1.html">Timeline </a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-animation"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-animation"></use>
                      </svg><span>Animation</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="animate.html">Animate</a></li>
                      <li><a href="scroll-reval.html">Scroll Reveal</a></li>
                      <li><a href="AOS.html">AOS animation</a></li>
                      <li><a href="tilt.html">Tilt Animation</a></li>
                      <li><a href="wow.html">Wow Animation</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-icons"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-icons"></use>
                      </svg><span>Icons</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="flag-icon.html">Flag icon</a></li>
                      <li><a href="font-awesome.html">Fontawesome Icon</a></li>
                      <li><a href="ico-icon.html">Ico Icon</a></li>
                      <li><a href="themify-icon.html">Themify Icon</a></li>
                      <li><a href="feather-icon.html">Feather icon</a></li>
                      <li><a href="whether-icon.html">Whether Icon</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-button"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-button"></use>
                      </svg><span>Buttons</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="buttons.html">Default Style</a></li>
                      <li><a href="button-group.html">Button Group</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-charts"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-charts"></use>
                      </svg><span>Charts</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="chart-apex.html">Apex Chart</a></li>
                      <li><a href="chart-google.html">Google Chart</a></li>
                      <li><a href="chart-sparkline.html">Sparkline chart</a></li>
                      <li><a href="chart-flot.html">Flot Chart</a></li>
                      <li><a href="chart-knob.html">Knob Chart</a></li>
                      <li><a href="chart-morris.html">Morris Chart</a></li>
                      <li><a href="chartjs.html">Chatjs Chart</a></li>
                      <li><a href="chartist.html">Chartist Chart</a></li>
                      <li><a href="chart-peity.html">Peity Chart</a></li>
                      <li><a href="echarts.html">Echarts</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-main-title">
                    <div>
                      <h6>Pages</h6>
                    </div>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="landing-page.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-landing-page"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-landing-page"></use>
                      </svg><span>Landing page</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="sample-page.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-sample-page"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-sample-page"></use>
                      </svg><span>Sample page</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="translate.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-internationalization"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-internationalization"></use>
                      </svg><span>Translate</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="https://admin.pixelstrap.net/zono/starter-kit/index.html" target="_blank">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-starter-kit"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-starter-kit"></use>
                      </svg><span>Starter kit</span></a></li>
                  <li class="mega-menu sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-others"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-others"></use>
                      </svg><span>Others</span></a>
                    <div class="mega-menu-container menu-content">
                      <div class="container-fluid">
                        <div class="row">
                          <div class="col mega-box">
                            <div class="link-section">
                              <div class="submenu-title">
                                <h5>Error Page</h5>
                              </div>
                              <ul class="submenu-content opensubmegamenu">
                                <li><a href="error-400.html">Error 400</a></li>
                                <li><a href="error-401.html">Error 401</a></li>
                                <li><a href="error-403.html">Error 403</a></li>
                                <li><a href="error-404.html">Error 404</a></li>
                                <li><a href="error-500.html">Error 500</a></li>
                                <li><a href="error-503.html">Error 503</a></li>
                              </ul>
                            </div>
                          </div>
                          <div class="col mega-box">
                            <div class="link-section">
                              <div class="submenu-title">
                                <h5> Authentication</h5>
                              </div>
                              <ul class="submenu-content opensubmegamenu">
                                <li><a href="login.html" target="_blank">Login Simple</a></li>
                                <li><a href="login_one.html" target="_blank">Login with bg image</a></li>
                                <li><a href="login_two.html" target="_blank">Login with image two                      </a></li>
                                <li><a href="login-bs-validation.html" target="_blank">Login With validation</a></li>
                                <li><a href="login-bs-tt-validation.html" target="_blank">Login with tooltip</a></li>
                                <li><a href="login-sa-validation.html" target="_blank">Login with sweetalert</a></li>
                                <li><a href="sign-up.html" target="_blank">Register Simple</a></li>
                                <li><a href="sign-up-one.html" target="_blank">Register with Bg Image                              </a></li>
                                <li><a href="sign-up-two.html" target="_blank">Register with image two</a></li>
                                <li><a href="sign-up-wizard.html" target="_blank">Register wizard</a></li>
                                <li><a href="unlock.html">Unlock User</a></li>
                                <li><a href="forget-password.html">Forget Password</a></li>
                                <li><a href="reset-password.html">Reset Password</a></li>
                                <li><a href="maintenance.html">Maintenance</a></li>
                              </ul>
                            </div>
                          </div>
                          <div class="col mega-box">
                            <div class="link-section">
                              <div class="submenu-title">
                                <h5>Coming Soon</h5>
                              </div>
                              <ul class="submenu-content opensubmegamenu">
                                <li><a href="comingsoon.html">Coming Simple</a></li>
                                <li><a href="comingsoon-bg-video.html">Coming with Bg video</a></li>
                                <li><a href="comingsoon-bg-img.html">Coming with Bg Image</a></li>
                              </ul>
                            </div>
                          </div>
                          <div class="col mega-box">
                            <div class="link-section">
                              <div class="submenu-title">
                                <h5>Email templates</h5>
                              </div>
                              <ul class="submenu-content opensubmegamenu">
                                <li><a href="basic-template.html">Basic Email</a></li>
                                <li><a href="email-header.html">Basic With Header</a></li>
                                <li><a href="template-email.html">Ecomerce Template</a></li>
                                <li><a href="template-email-2.html">Email Template 2</a></li>
                                <li><a href="ecommerce-templates.html">Ecommerce Email</a></li>
                                <li><a href="email-order-success.html">Order Success</a></li>
                              </ul>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                  <li class="sidebar-main-title">
                    <div>
                      <h6>Miscellaneous</h6>
                    </div>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-gallery"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-gallery"></use>
                      </svg><span>Gallery</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="gallery.html">Gallery Grid</a></li>
                      <li><a href="gallery-with-description.html">Gallery Grid Desc</a></li>
                      <li><a href="gallery-masonry.html">Masonry Gallery</a></li>
                      <li><a href="masonry-gallery-with-disc.html">Masonry with Desc</a></li>
                      <li><a href="gallery-hover.html">Hover Effects</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-blog"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-blog"></use>
                      </svg><span>Blog</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="blog.html">Blog Details</a></li>
                      <li><a href="blog-single.html">Blog Single</a></li>
                      <li><a href="add-post.html">Add Post</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="faq.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-faq"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-faq"></use>
                      </svg><span>FAQ</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-job-search"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-job-search"></use>
                      </svg><span>Job Search</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="job-cards-view.html">Cards view</a></li>
                      <li><a href="job-list-view.html">List View</a></li>
                      <li><a href="job-details.html">Job Details</a></li>
                      <li><a href="job-apply.html">Apply</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-learning"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-learning"></use>
                      </svg><span>Learning</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="learning-list-view.html">Learning List</a></li>
                      <li><a href="learning-detailed.html">Detailed Course</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-maps"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-maps"></use>
                      </svg><span>Maps</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="map-js.html">Maps JS</a></li>
                      <li><a href="vector-map.html">Vector Maps</a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title" href="#">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-editors"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-editors"></use>
                      </svg><span>Editors</span></a>
                    <ul class="sidebar-submenu">
                      <li><a href="summernote.html">Summer Note</a></li>
                      <li><a href="ckeditor.html">CK editor</a></li>
                      <li><a href="simple-MDE.html">MDE editor</a></li>
                      <li><a href="ace-code-editor.html">ACE code editor </a></li>
                    </ul>
                  </li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="knowledgebase.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-knowledgebase"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-knowledgebase"></use>
                      </svg><span>Knowledgebase</span></a></li>
                  <li class="sidebar-list"><i class="fa fa-thumb-tack"></i><a class="sidebar-link sidebar-title link-nav" href="support-ticket.html">
                      <svg class="stroke-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-support-tickets"></use>
                      </svg>
                      <svg class="fill-icon">
                        <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#fill-support-tickets"></use>
                      </svg><span>Support Ticket</span></a></li>
                </ul>
              </div>
              <div class="right-arrow" id="right-arrow"><i data-feather="arrow-right"></i></div>
            </nav>
          </div>
        </div>
        <!-- Page Sidebar Ends-->
        <div class="page-body">
          <div class="container-fluid">
            <div class="page-title">
              <div class="row">
                <div class="col-xl-4 col-sm-7 box-col-3">
                  <h3>Form Wizard</h3>
                </div>
                <div class="col-5 d-none d-xl-block">
                  <!-- Page Sub Header Start-->
                  <div class="left-header main-sub-header p-0">
                    <div class="left-menu-header">
                      <ul class="header-left"> 
                        <li class="onhover-dropdown"> <span class="f-w-700">General </span>
                          <ul class="onhover-show-div left-dropdown">
                            <li class="flyout-right"><a href="#">Dashboards</a><i class="fa fa-angle-right"></i>
                              <ul>
                                <li> <a href="index-2.html">Default  </a></li>
                                <li> <a href="dashboard-02.html">Ecommerce</a></li>
                              </ul>
                            </li>
                            <li class="flyout-right"><a href="#">Widgets</a><i class="fa fa-angle-right"></i>
                              <ul>
                                <li><a href="general-widget.html">General</a></li>
                                <li> <a href="chart-widget.html">chart</a></li>
                              </ul>
                            </li>
                            <li class="flyout-right"> <a href="#">Page layout</a><i class="fa fa-angle-right"></i>
                              <ul>
                                <li> <a href="box-layout.html">Boxed </a></li>
                                <li> <a href="layout-rtl.html">RTL</a></li>
                                <li> <a href="layout-dark.html">Dark Layout</a></li>
                                <li> <a href="footer-light.html">footer-light.html</a></li>
                                <li> <a href="footer-dark.html">footer-dark.html</a></li>
                                <li><a href="footer-fixed.html">footer-fixed.html</a></li>
                              </ul>
                            </li>
                          </ul>
                        </li>
                        <li class="onhover-dropdown"><span class="f-w-700">Components</span>
                          <ul class="onhover-show-div left-dropdown">
                            <li class="flyout-right"><a href="#">Ui Kits</a>
                              <ul>
                                <li><a href="typography.html">Typography</a></li>
                                <li><a href="avatars.html">Avatars</a></li>
                                <li><a href="helper-classes.html">helper classes</a></li>
                                <li><a href="grid.html">Grid</a></li>
                                <li><a href="tag-pills.html">Tag & pills</a></li>
                                <li><a href="progress-bar.html">Progress</a></li>
                                <li><a href="modal.html">Modal</a></li>
                                <li><a href="alert.html">Alert</a></li>
                                <li><a href="popover.html">Popover</a></li>
                                <li><a href="tooltip.html">Tooltip</a></li>
                                <li><a href="dropdown.html">Dropdown</a></li>
                                <li><a href="according.html">Accordion</a></li>
                                <li><a href="tab-bootstrap.html">Tabs</a></li>
                                <li><a href="list.html">Lists</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Bonus Ui</a>
                              <ul>
                                <li><a href="scrollable.html">Scrollable</a></li>
                                <li><a href="tree.html">Tree view</a></li>
                                <li><a href="toasts.html">Toasts</a></li>
                                <li><a href="rating.html">Rating</a></li>
                                <li><a href="dropzone.html">dropzone</a></li>
                                <li><a href="tour.html">Tour</a></li>
                                <li><a href="sweet-alert2.html">SweetAlert2</a></li>
                                <li><a href="modal-animated.html">Animated Modal</a></li>
                                <li><a href="owl-carousel.html">Owl Carousel</a></li>
                                <li><a href="ribbons.html">Ribbons</a></li>
                                <li><a href="pagination.html">Pagination</a></li>
                                <li><a href="breadcrumb.html">Breadcrumb</a></li>
                                <li><a href="range-slider.html">Range Slider</a></li>
                                <li><a href="image-cropper.html">Image cropper</a></li>
                                <li><a href="basic-card.html">Basic Card</a></li>
                                <li><a href="creative-card.html">Creative Card</a></li>
                                <li><a href="dragable-card.html">Draggable Card</a></li>
                                <li><a href="timeline-v-1.html">Timeline </a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Animation</a>
                              <ul>
                                <li><a href="animate.html">Animate</a></li>
                                <li><a href="scroll-reval.html">Scroll Reveal</a></li>
                                <li><a href="AOS.html">AOS animation</a></li>
                                <li><a href="tilt.html">Tilt Animation</a></li>
                                <li><a href="wow.html">Wow Animation</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Icons</a>
                              <ul>
                                <li><a href="flag-icon.html">Flag icon</a></li>
                                <li><a href="font-awesome.html">Fontawesome Icon</a></li>
                                <li><a href="ico-icon.html">Ico Icon</a></li>
                                <li><a href="themify-icon.html">Themify Icon</a></li>
                                <li><a href="feather-icon.html">Feather icon</a></li>
                                <li><a href="whether-icon.html">Whether Icon</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Buttons</a>
                              <ul>
                                <li><a href="buttons.html">Default Style</a></li>
                                <li><a href="button-group.html">Button Group</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Charts</a>
                              <ul>
                                <li><a href="echarts.html">Echarts</a></li>
                                <li><a href="chart-apex.html">Apex Chart</a></li>
                                <li><a href="chart-google.html">Google Chart</a></li>
                                <li><a href="chart-sparkline.html">Sparkline chart</a></li>
                                <li><a href="chart-flot.html">Flot Chart </a></li>
                                <li><a href="chart-knob.html">Knob Chart</a></li>
                                <li><a href="chart-morris.html">Morris Chart</a></li>
                                <li><a href="chartjs.html">Chatjs Chart</a></li>
                                <li><a href="chartist.html">Chartist Chart</a></li>
                                <li><a href="chart-peity.html">Peity Chart</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                          </ul>
                        </li>
                        <li class="onhover-dropdown"> <span class="f-w-700">Applications</span>
                          <ul class="onhover-show-div left-dropdown">
                            <li class="flyout-right"><a href="#">Project</a>
                              <ul>
                                <li><a href="projects.html">Project List</a></li>
                                <li><a href="projectcreate.html">Create new</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li><a href="file-manager.html">File manager</a></li>
                            <li><a href="kanban.html">kanban Board </a></li>
                            <li class="flyout-right"> <a href="#">Ecommerce </a>
                              <ul>
                                <li><a href="add-products.html">Add Product</a></li>
                                <li><a href="product.html">Product</a></li>
                                <li><a href="product-page.html">Product page</a></li>
                                <li><a href="list-products.html">Product list</a></li>
                                <li><a href="payment-details.html">Payment Details</a></li>
                                <li><a href="order-history.html">Order History</a></li>
                                <li class="flyout-right"><a class="submenu-title" href="#">Invoices</a>
                                  <ul>
                                    <li><a href="invoice-1.html">Invoice-1</a></li>
                                    <li><a href="invoice-2.html">Invoice-2</a></li>
                                    <li><a href="invoice-3.html">Invoice-3</a></li>
                                    <li><a href="invoice-4.html">Invoice-4</a></li>
                                    <li><a href="invoice-5.html">Invoice-5</a></li>
                                    <li><a href="invoice-template.html">Invoice-6</a></li>
                                  </ul>
                                </li>
                                <li><a href="cart.html">Cart</a></li>
                                <li><a href="list-wish.html">Wishlist</a></li>
                                <li><a href="checkout.html">Checkout</a></li>
                                <li><a href="pricing.html">Pricing  </a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Email</a>
                              <ul>
                                <li><a href="letter-box.html">Email App</a></li>
                                <li><a href="email-compose.html">Email Compose</a></li>
                                <li><a href="letter-box.html">Letter Box</a></li>
                              </ul><i class="fa fa-angle-right"> </i>
                            </li>
                            <li class="flyout-right"> <a href="#">Chat</a>
                              <ul>
                                <li><a href="private-chat.html">Private Chat</a></li>
                                <li><a href="group-chat.html">Group Chat</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">User</a>
                              <ul>
                                <li><a href="user-profile.html">Users Profile</a></li>
                                <li><a href="edit-profile.html">Users Edit</a></li>
                                <li><a href="user-cards.html">Users Cards</a></li>
                              </ul><i class="fa fa-angle-right"> </i>
                            </li>
                            <li><a href="bookmark.html">Bookmarks</a></li>
                            <li><a href="contacts.html">Contacts</a></li>
                            <li><a href="task.html">Task</a></li>
                            <li><a href="calendar-basic.html">Calendar</a></li>
                            <li><a href="social-app.html">Social-app</a></li>
                            <li><a href="to-do.html">To-Do</a></li>
                            <li><a href="search.html">Search Result</a></li>
                          </ul>
                        </li>
                        <li class="onhover-dropdown"><span class="f-w-700">Pages</span>
                          <ul class="onhover-show-div left-dropdown">
                            <li><a href="blog.html">Landing page</a></li>
                            <li><a href="blog-single.html">Sample page</a></li>
                            <li><a href="add-post.html">Starter kit</a></li>
                            <li class="flyout-right"><a href="#">Others </a><i class="fa fa-angle-right"></i>
                              <ul>
                                <li class="flyout-right"><a href="#">Error Page</a>
                                  <ul>
                                    <li><a href="error-400.html">Error 400</a></li>
                                    <li><a href="error-401.html">Error 401</a></li>
                                    <li><a href="error-403.html">Error 403</a></li>
                                    <li><a href="error-404.html">Error 404</a></li>
                                    <li><a href="error-500.html">Error 500</a></li>
                                    <li><a href="error-503.html">Error 503</a></li>
                                  </ul><i class="fa fa-angle-right"> </i>
                                </li>
                                <li class="flyout-right"> <a href="#">Authentication</a>
                                  <ul>
                                    <li><a href="login.html" target="_blank">Login Simple</a></li>
                                    <li><a href="login_one.html" target="_blank">Login with bg image</a></li>
                                    <li><a href="login_two.html" target="_blank">Login with image two                      </a></li>
                                    <li><a href="login-bs-validation.html" target="_blank">Login With validation</a></li>
                                    <li><a href="login-bs-tt-validation.html" target="_blank">Login with tooltip</a></li>
                                    <li><a href="login-sa-validation.html" target="_blank">Login with sweetalert</a></li>
                                    <li><a href="sign-up.html" target="_blank">Register Simple</a></li>
                                    <li><a href="sign-up-one.html" target="_blank">Register with Bg Image                              </a></li>
                                    <li><a href="sign-up-two.html" target="_blank">Register with image two</a></li>
                                    <li><a href="sign-up-wizard.html" target="_blank">Register wizard</a></li>
                                    <li><a href="unlock.html">Unlock User</a></li>
                                    <li><a href="forget-password.html">Forget Password</a></li>
                                    <li><a href="reset-password.html">Reset Password</a></li>
                                    <li><a href="maintenance.html">Maintenance</a></li>
                                  </ul>
                                </li>
                                <li class="flyout-right"> <a href="#">Coming Soon</a>
                                  <ul> 
                                    <li><a href="comingsoon.html">Coming Simple</a></li>
                                    <li><a href="comingsoon-bg-video.html">Coming with Bg video</a></li>
                                    <li><a href="comingsoon-bg-img.html">Coming with Bg Image</a></li>
                                  </ul><i class="fa fa-angle-right"> </i>
                                </li>
                                <li class="flyout-right"><a href="#">Email templates</a>
                                  <ul>
                                    <li><a href="basic-template.html">Basic Email</a></li>
                                    <li><a href="email-header.html">Basic With Header</a></li>
                                    <li><a href="template-email.html">Ecomerce Template</a></li>
                                    <li><a href="template-email-2.html">Email Template 2</a></li>
                                    <li><a href="ecommerce-templates.html">Ecommerce Email</a></li>
                                    <li><a href="email-order-success.html">Order Success</a></li>
                                  </ul><i class="fa fa-angle-right"></i>
                                </li>
                              </ul>
                            </li>
                          </ul>
                        </li>
                        <li class="onhover-dropdown p-0"><span class="f-w-700">Miscellaneous</span>
                          <ul class="onhover-show-div left-dropdown">
                            <li class="flyout-right"><a href="#">Gallery</a>
                              <ul> 
                                <li><a href="gallery.html">Gallery Grid</a></li>
                                <li><a href="gallery-with-description.html">gallery-with-description</a></li>
                                <li><a href="gallery-masonry.html">gallery-masonry</a></li>
                                <li><a href="masonry-gallery-with-disc.html">masonry-gallery-with-disc</a></li>
                                <li><a href="gallery-hover.html">gallery-hover</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Blog</a>
                              <ul>
                                <li><a href="blog.html">blog</a></li>
                                <li><a href="blog-single.html">blog-single</a></li>
                                <li><a href="add-post.html">add-post</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li><a href="FAQ-2.html">FAQ</a></li>
                            <li class="flyout-right"><a href="#">Job Search</a>
                              <ul>
                                <li><a href="job-cards-view.html">job-cards-view</a></li>
                                <li><a href="job-list-view.html">job-list-view</a></li>
                                <li><a href="job-details.html">job-details</a></li>
                                <li><a href="job-apply.html">job-apply</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Job Search</a>
                              <ul> 
                                <li><a href="job-cards-view.html">learning-list</a></li>
                                <li><a href="learning-detailed.html">learning-detailed</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Maps</a>
                              <ul>
                                <li><a href="map-js.html">Map-js</a></li>
                                <li><a href="vector-map.html">Vector Maps</a></li>
                              </ul><i class="fa fa-angle-right"></i>
                            </li>
                            <li class="flyout-right"><a href="#">Editors </a>
                              <ul>
                                <li><a href="summernote.html">Summer Note</a></li>
                                <li><a href="CK%20editor.html">CK editor</a></li>
                                <li><a href="simple-MDE.html">MDE editor</a></li>
                                <li><a href="ace-code-editor.html">ACE code editor </a></li>
                              </ul><i class="fa fa-angle-right"> </i>
                            </li>
                            <li><a href="knowledgebase.html">Knowledgebase </a></li>
                            <li> <a href="support-ticket-2.html">Support Ticket</a></li>
                          </ul>
                        </li>
                      </ul>
                    </div>
                  </div>
                  <!-- Page Sub Header end
                  -->
                </div>
                <div class="col-xl-3 col-sm-5 box-col-4"> 
                  <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="index-2.html">
                        <svg class="stroke-icon">
                          <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#stroke-home"></use>
                        </svg></a></li>
                    <li class="breadcrumb-item">Form Layout</li>
                    <li class="breadcrumb-item active">Form Wizard </li>
                  </ol>
                </div>
              </div>
            </div>
          </div>
          <!-- Container-fluid starts-->
          <div class="container-fluid">
            <div class="row">
              <div class="col-xl-6">
                <div class="card height-equal">
                  <div class="card-header pb-0">
                    <h4>Numbering wizard </h4>
                    <p class="f-m-light mt-1">
                       Fill up your details and proceed next steps.</p>
                  </div>
                  <div class="card-body basic-wizard important-validation">
                    <div class="stepper-horizontal custom-scrollbar" id="stepper1">
                      <div class="stepper-one stepper step editing active">
                        <div class="step-circle"><span>1</span></div>
                        <div class="step-title">Basic Info</div>
                        <div class="step-bar-left"></div>
                        <div class="step-bar-right"></div>
                      </div>
                      <div class="stepper-two step">
                        <div class="step-circle"><span>2</span></div>
                        <div class="step-title">Cart Info</div>
                        <div class="step-bar-left"></div>
                        <div class="step-bar-right"></div>
                      </div>
                      <div class="stepper-three step">
                        <div class="step-circle"><span>3</span></div>
                        <div class="step-title">Feedback</div>
                        <div class="step-bar-left"></div>
                        <div class="step-bar-right"></div>
                      </div>
                      <div class="stepper-four step">
                        <div class="step-circle"><span>4</span></div>
                        <div class="step-title">Finish</div>
                        <div class="step-bar-left"></div>
                        <div class="step-bar-right"></div>
                      </div>
                    </div>
                    <div id="msform">
                      <form class="stepper-one row g-3 needs-validation custom-input" novalidate="">
                        <div class="col-sm-6">
                          <label class="form-label" for="email-basic-wizard">Email<span class="txt-danger">*</span></label>
                          <input class="form-control" id="email-basic-wizard" type="email" required="" placeholder="<EMAIL>">
                        </div>
                        <div class="col-sm-6">
                          <label class="form-label" for="firstnamewizard">First Name<span class="txt-danger">*</span></label>
                          <input class="form-control" id="firstnamewizard" type="text" required="" placeholder="Enter your name">
                        </div>
                        <div class="col-12">
                          <label class="col-sm-12 form-label" for="passwordwizard">Password<span class="txt-danger">*</span></label>
                          <input class="form-control" id="passwordwizard" type="password" placeholder="Enter password" required="">
                        </div>
                        <div class="col-12">
                          <label class="col-sm-12 form-label" for="confirmpasswordwizard">Confirm Password<span class="txt-danger">*</span></label>
                          <input class="form-control" id="confirmpasswordwizard" type="password" placeholder="Enter confirm password" required="">
                        </div>
                        <div class="col-12">
                          <div class="form-check">
                            <input class="form-check-input" id="inputcheckwizard" type="checkbox" value="" required="">
                            <label class="form-check-label mb-0" for="inputcheckwizard">Agree to terms and conditions</label>
                          </div>
                        </div>
                      </form>
                      <form class="stepper-two row g-3 needs-validation custom-input" novalidate="">
                        <div class="col-md-12"> 
                          <label class="form-label" for="placeholdername1">Placeholder Name </label>
                          <input class="form-control" id="placeholdername1" type="text" required="" placeholder="Placeholder name">
                        </div>
                        <div class="col-xxl-4 col-sm-6">
                          <label class="form-label" for="cardNumber01">Card Number</label>
                          <input class="form-control" id="cardNumber01" type="text" required="" placeholder="xxxx xxxx xxxx xxxx">
                        </div>
                        <div class="col-xxl-4 col-sm-6">
                          <label class="form-label" for="expirationDates01">Expiration(MM/YY)</label>
                          <input class="form-control" id="expirationDates01" type="number" required="" placeholder="xx/xx">
                        </div>
                        <div class="col-xxl-4">
                          <label class="form-label" for="cvvNumber-a">CVV Number</label>
                          <input class="form-control" id="cvvNumber-a" type="text" required="">
                        </div>
                        <div class="col-12"> 
                          <label class="form-label" for="formFileDocument">Upload Documentation</label>
                          <input class="form-control" id="formFileDocument" type="file" aria-label="file example" required="">
                        </div>
                        <div class="col-12">
                          <div class="form-check">
                            <input class="form-check-input" id="invalidCheck-a" type="checkbox" value="" required="">
                            <label class="form-check-label" for="invalidCheck-a">All the above information is correct</label>
                          </div>
                        </div>
                      </form>
                      <form class="stepper-three row g-3 needs-validation custom-input" novalidate="">
                        <div class="col-sm-6">
                          <label class="form-label" for="email-basic">LinkedIn<span class="txt-danger">*</span></label>
                          <input class="form-control" id="email-basic" type="url" required="" placeholder="https://linkedIn.com/Zono">
                          <div class="invalid-feedback">Please enter your valid link</div>
                          <div class="valid-feedback">
                             Looks's Good!</div>
                        </div>
                        <div class="col-sm-6">
                          <label class="form-label" for="validationCustom996">Github<span class="txt-danger">*</span></label>
                          <input class="form-control" id="validationCustom996" type="url" required="" placeholder="https://github.com/Zono">
                          <div class="invalid-feedback">Please enter your valid link</div>
                          <div class="valid-feedback">
                             Looks's Good!     </div>
                        </div>
                        <div class="col-12"> 
                          <label class="form-label" for="validationCustom09">Select State<span class="txt-danger">*</span></label>
                          <select class="form-select" id="validationCustom09" required="">
                            <option selected="" disabled="" value="">Choose...</option>
                            <option>U.K </option>
                            <option>U.S </option>
                            <option>India</option>
                          </select>
                          <div class="invalid-feedback">Please select a valid state.</div>
                        </div>
                        <div class="col-12"> 
                          <label class="form-label" for="givefeedback">Give Feedback</label>
                          <textarea class="form-control" id="givefeedback" required=""></textarea>
                          <div class="invalid-feedback">Please enter a message in the textarea.</div>
                        </div>
                        <div class="col-12">
                          <div class="form-check">
                            <input class="form-check-input" id="invalidCheck46" type="checkbox" value="" required="">
                            <label class="form-check-label mb-0" for="invalidCheck46">Agree to terms and conditions</label>
                            <div class="invalid-feedback">You must agree before submitting.</div>
                          </div>
                        </div>
                      </form>
                      <form class="stepper-four row g-3 needs-validation" novalidate="">
                        <div class="col-12 m-0">
                          <div class="successful-form"><img class="img-fluid" src="../assets/images/gif/dashboard-8/successful.gif" alt="successful">
                            <h3>Congratulations </h3>
                            <p>Well done! You have successfully completed. </p>
                          </div>
                        </div>
                      </form>
                    </div>
                    <div class="wizard-footer d-flex gap-2 justify-content-end">
                      <button class="btn alert-light-primary" id="backbtn" onclick="backStep()"> Back</button>
                      <button class="btn btn-primary" id="nextbtn" onclick="nextStep()">Next</button>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-xl-6">
                <div class="card height-equal">
                  <div class="card-header pb-0">
                    <h4>Student validation form</h4>
                    <p class="f-m-light mt-1">
                        Please make sure fill all the filed before click on next button.</p>
                  </div>
                  <div class="card-body custom-input">
                    <form class="form-wizard" id="regForm" action="#" method="POST">
                      <div class="tab">
                        <div class="row g-3">
                          <div class="col-sm-6">
                            <label for="name">Name</label>
                            <input class="form-control" id="name" type="text" placeholder="Enter your name" required="required">
                          </div>
                          <div class="col-sm-6">
                            <label class="form-label" for="student-email-wizard">Email<span class="txt-danger">*</span></label>
                            <input class="form-control" id="student-email-wizard" type="email" required="" placeholder="<EMAIL>">
                          </div>
                          <div class="col-12">
                            <label class="col-sm-12 form-label" for="password-wizard">Password<span class="txt-danger">*</span></label>
                            <input class="form-control" id="password-wizard" type="password" placeholder="Enter password" required="">
                          </div>
                          <div class="col-12">
                            <label class="col-sm-12 form-label" for="confirmpassowrd">Confirm Password<span class="txt-danger">*</span></label>
                            <input class="form-control" id="confirmpassowrd" type="password" placeholder="Enter confirm password" required="">
                          </div>
                        </div>
                      </div>
                      <div class="tab">
                        <div class="row g-3 avatar-upload">
                          <div class="col-12">
                            <div>
                              <div class="avatar-edit">
                                <input id="imageUpload" type="file" accept=".png, .jpg, .jpeg">
                                <label for="imageUpload"></label>
                              </div>
                              <div class="avatar-preview">
                                <div id="image"></div>
                              </div>
                            </div>
                            <h3>Add Profile</h3>
                          </div>
                          <div class="col-12">
                            <label class="form-label" for="exampleFormControlInput1">Portfolio URL</label>
                            <input class="form-control" id="exampleFormControlInput1" type="url" placeholder="https://zono/">
                          </div>
                          <div class="col-12"> 
                            <label class="form-label" for="projectDescription">Project Description</label>
                            <textarea class="form-control" id="projectDescription" rows="2"></textarea>
                          </div>
                        </div>
                      </div>
                      <div class="tab">
                        <h5 class="mb-2">Social Links </h5>
                        <div class="row g-3">
                          <div class="col-sm-6">
                            <label class="form-label" for="twitterControlInput">Twitter</label>
                            <input class="form-control" id="twitterControlInput" type="url" placeholder="https://twitter.com/">
                          </div>
                          <div class="col-sm-6">
                            <label class="form-label" for="githubControlInput">Github</label>
                            <input class="form-control" id="githubControlInput" type="url" placeholder="https://admin.pixelstrap.net/github.com">
                          </div>
                          <div class="col-12"> 
                            <div class="input-group">
                              <input class="form-control" id="inputGroupFile04" type="file" aria-describedby="inputGroupFileAddon04" aria-label="Upload">
                              <button class="btn btn-outline-secondary" id="inputGroupFileAddon04" type="button">Submit</button>
                            </div>
                          </div>
                          <div class="col-12">
                            <select class="form-select" aria-label="Default select example">
                              <option selected="">Positions</option>
                              <option value="1">Web Designer</option>
                              <option value="2">Software Engineer</option>
                              <option value="3">UI/UX Designer </option>
                              <option value="3">Web Developer</option>
                            </select>
                          </div>
                          <div class="col-12"> 
                            <label class="form-label" for="quationsTextarea">Why do you want to take this position?</label>
                            <textarea class="form-control" id="quationsTextarea" rows="2"></textarea>
                          </div>
                        </div>
                      </div>
                      <div>
                        <div class="text-end pt-3">
                          <button class="btn btn-secondary" id="prevBtn" type="button" onclick="nextPrev(-1)">Previous</button>
                          <button class="btn btn-primary" id="nextBtn" type="button" onclick="nextPrev(1)">Next</button>
                        </div>
                      </div>
                      <!-- Circles which indicates the steps of the form:-->
                      <div class="text-center"><span class="step"></span><span class="step"></span><span class="step"></span><span class="step"></span></div>
                    </form>
                  </div>
                </div>
              </div>
              <div class="col-md-12">
                <div class="card">
                  <div class="card-header pb-0">
                    <h4>Vertical validation wizard </h4>
                    <p class="f-m-light mt-1">
                       Fill up your true details and next proceed.</p>
                  </div>
                  <div class="card-body">
                    <div class="vertical-main-wizard">
                      <div class="row g-3">    
                        <div class="col-xxl-3 col-xl-4 col-12">
                          <div class="nav flex-column header-vertical-wizard" id="wizard-tab" role="tablist" aria-orientation="vertical"><a class="nav-link" id="wizard-contact-tab" data-bs-toggle="pill" href="#wizard-contact" role="tab" aria-controls="wizard-contact" aria-selected="true"> 
                              <div class="vertical-wizard">
                                <div class="stroke-icon-wizard"><i class="fa fa-user"></i></div>
                                <div class="vertical-wizard-content"> 
                                  <h3>Your Info</h3>
                                  <p>Add your details </p>
                                </div>
                              </div></a><a class="nav-link" id="wizard-cart-tab" data-bs-toggle="pill" href="#wizard-cart" role="tab" aria-controls="wizard-cart" aria-selected="false"> 
                              <div class="vertical-wizard">
                                <div class="stroke-icon-wizard"><i class="fa fa-chain-broken"></i></div>
                                <div class="vertical-wizard-content"> 
                                  <h3>Cart Info</h3>
                                  <p>Add your a/c details</p>
                                </div>
                              </div></a><a class="nav-link" id="wizard-banking-tab" data-bs-toggle="pill" href="#wizard-banking" role="tab" aria-controls="wizard-banking" aria-selected="false"> 
                              <div class="vertical-wizard">
                                <div class="stroke-icon-wizard"><i class="fa fa-group"></i></div>
                                <div class="vertical-wizard-content"> 
                                  <h3>Net Banking</h3>
                                  <p>Choose your bank</p>
                                </div>
                              </div></a></div>
                        </div>
                        <div class="col-xxl-9 col-xl-8 col-12">
                          <div class="tab-content" id="wizard-tabContent">
                            <div class="tab-pane fade show active" id="wizard-contact" role="tabpanel" aria-labelledby="wizard-contact-tab">
                              <form class="row g-3 needs-validation custom-input" novalidate="">
                                <div class="col-xxl-4 col-sm-6">
                                  <label class="form-label" for="validationCustom0-a">First name<span class="txt-danger">*</span></label>
                                  <input class="form-control" id="validationCustom0-a" type="text" placeholder="Enter first name" required="">
                                  <div class="valid-feedback">Looks good!</div>
                                </div>
                                <div class="col-xxl-4 col-sm-6">
                                  <label class="form-label" for="validationCustom-b">Last name<span class="txt-danger">*</span></label>
                                  <input class="form-control" id="validationCustom-b" type="text" placeholder="Enter last name" required="">
                                  <div class="valid-feedback">Looks good!</div>
                                </div>
                                <div class="col-xxl-4 col-sm-6">
                                  <label class="form-label" for="validationemail-b">Email<span class="txt-danger">*</span></label>
                                  <input class="form-control" id="validationemail-b" type="email" required="" placeholder="<EMAIL>">
                                  <div class="valid-feedback">Looks good!</div>
                                </div>
                                <div class="col-xxl-5 col-sm-6">
                                  <label class="form-label" for="validationCustom04">State</label>
                                  <select class="form-select" id="validationCustom04" required="">
                                    <option selected="" disabled="" value="">Choose...</option>
                                    <option>USA </option>
                                    <option>U.K </option>
                                    <option>U.S</option>
                                  </select>
                                  <div class="invalid-feedback">Please select a valid state.</div>
                                </div>
                                <div class="col-xxl-3 col-sm-6">
                                  <label class="form-label" for="validationCustom05">Zip Code</label>
                                  <input class="form-control" id="validationCustom05" type="text" required="">
                                  <div class="invalid-feedback">Please provide a valid zip.</div>
                                </div>
                                <div class="col-xxl-4 col-sm-6">
                                  <label class="form-label" for="contactnumber">Contact Number</label>
                                  <input class="form-control" id="contactnumber" type="number" placeholder="Enter number" required="">
                                  <div class="valid-feedback">Looks good!</div>
                                </div>
                                <div class="col-12">
                                  <div class="form-check">
                                    <input class="form-check-input" id="invalidCheck-n" type="checkbox" value="" required="">
                                    <label class="form-check-label" for="invalidCheck-n">Agree to terms and conditions</label>
                                    <div class="invalid-feedback">You must agree before submitting.</div>
                                  </div>
                                </div>
                                <div class="col-12 text-end"> 
                                  <button class="btn btn-primary">Next</button>
                                </div>
                              </form>
                            </div>
                            <div class="tab-pane fade" id="wizard-cart" role="tabpanel" aria-labelledby="wizard-cart-tab">
                              <form class="row g-3 needs-validation custom-input" novalidate="">
                                <div class="col-xxl-6">
                                  <div class="card-wrapper border rounded-3 checkbox-checked">
                                    <h3 class="sub-title">Select your payment method</h3>
                                    <div class="radio-form">
                                      <div class="form-check">
                                        <input class="form-check-input" id="flexRadioDefault1" type="radio" name="flexRadioDefault-a">
                                        <label class="form-check-label" for="flexRadioDefault1">Visa</label>
                                      </div>
                                      <div class="form-check">
                                        <input class="form-check-input" id="flexRadioDefault2" type="radio" name="flexRadioDefault-a" checked="">
                                        <label class="form-check-label" for="flexRadioDefault2">MasterCard</label>
                                      </div>
                                      <div class="form-check">
                                        <input class="form-check-input" id="flexRadioDefault3" type="radio" name="flexRadioDefault-a" checked="">
                                        <label class="form-check-label" for="flexRadioDefault3">Paypal</label>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-xxl-6"> 
                                  <div class="row"> 
                                    <div class="col-12">
                                      <div class="input-group mb-3">
                                        <input class="form-control" type="text" placeholder="Recipient's username" aria-label="Recipient's username" aria-describedby="button-addon2">
                                        <button class="btn btn-outline-secondary" id="button-addon2" type="button">Submit</button>
                                      </div>
                                    </div>
                                    <div class="col-12"> 
                                      <div class="input-group"><span class="input-group-text" id="basic-addon1">@</span>
                                        <input class="form-control" type="text" placeholder="Username" aria-label="Username" aria-describedby="basic-addon1">
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="col-md-4 col-sm-6">
                                  <label class="form-label" for="txtCardNumber1">Card Number</label>
                                  <input class="form-control" id="txtCardNumber1" type="text" required="" placeholder="xxxx xxxx xxxx xxxx">
                                  <div class="invalid-feedback">Please enter your valid number</div>
                                  <div class="valid-feedback">
                                     Looks's Good!</div>
                                </div>
                                <div class="col-md-4 col-sm-6">
                                  <label class="form-label" for="validationDates">Expiration(MM/YY)</label>
                                  <input class="form-control" id="validationDates" type="number" required="" placeholder="xx/xx">
                                  <div class="invalid-feedback">Please enter your valid number</div>
                                  <div class="valid-feedback">
                                     Looks's Good!</div>
                                </div>
                                <div class="col-md-4 col-sm-6">
                                  <label class="form-label" for="cvvNumber-b">CVV</label>
                                  <input class="form-control" id="cvvNumber-b" type="text" required="">
                                  <div class="invalid-feedback">Please enter your valid number</div>
                                  <div class="valid-feedback">
                                     Looks's Good!</div>
                                </div>
                                <div class="col-md-12 col-sm-6">
                                  <label class="form-label" for="formFile2">Upload Documentation</label>
                                  <input class="form-control" id="formFile2" type="file" aria-label="file example" required="">
                                  <div class="invalid-feedback">Invalid form file selected</div>
                                </div>
                                <div class="col-12">
                                  <div class="form-check mb-0">
                                    <input class="form-check-input" id="invalidCheck-b" type="checkbox" value="" required="">
                                    <label class="form-check-label" for="invalidCheck-b">All the above information is correct</label>
                                    <div class="invalid-feedback">You must agree before submitting.</div>
                                  </div>
                                </div>
                                <div class="col-12 text-end"> 
                                  <button class="btn btn-primary">Previous</button>
                                  <button class="btn btn-primary">Next</button>
                                </div>
                              </form>
                            </div>
                            <div class="tab-pane fade custom-input" id="wizard-banking" role="tabpanel" aria-labelledby="wizard-banking-tab">
                              <form class="row g-3 mb-3 needs-validation" novalidate="">
                                <div class="col-md-12"> 
                                  <div class="accordion dark-accordion" id="accordionExample-a">
                                    <div class="accordion-item">
                                      <h2 class="accordion-header" id="headingOne-a">
                                        <button class="accordion-button collapsed accordion-light-primary txt-primary" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne-a" aria-expanded="true" aria-controls="collapseOne-a">NET BANKING<i class="svg-color" data-feather="chevron-down"></i></button>
                                      </h2>
                                      <div class="accordion-collapse collapse" id="collapseOne-a" aria-labelledby="headingOne-a" data-bs-parent="#accordionExample-a">
                                        <div class="accordion-body weight-title card-wrapper">
                                          <h3 class="sub-title f-14">SELECT YOUR BANK</h3>
                                          <div class="row choose-bank">
                                            <div class="col-sm-6">
                                              <div class="form-check radio radio-primary">
                                                <input class="form-check-input" id="flexRadioDefault-z" type="radio" name="flexRadioDefault-v">
                                                <label class="form-check-label" for="flexRadioDefault-z">Industrial & Commercial Bank</label>
                                              </div>
                                              <div class="form-check radio radio-primary">
                                                <input class="form-check-input" id="flexRadioDefault-y" type="radio" name="flexRadioDefault-v">
                                                <label class="form-check-label" for="flexRadioDefault-y">Agricultural Bank</label>
                                              </div>
                                              <div class="form-check radio radio-primary">
                                                <input class="form-check-input" id="flexRadioDefault-x" type="radio" name="flexRadioDefault-v" checked="">
                                                <label class="form-check-label" for="flexRadioDefault-x">JPMorgan Chase & Co.</label>
                                              </div>
                                            </div>
                                            <div class="col-sm-6"> 
                                              <div class="form-check radio radio-primary">
                                                <input class="form-check-input" id="flexRadioDefault-w" type="radio" name="flexRadioDefault-v">
                                                <label class="form-check-label" for="flexRadioDefault-w">Construction Bank Corp.</label>
                                              </div>
                                              <div class="form-check radio radio-primary">
                                                <input class="form-check-input" id="flexRadioDefault-v" type="radio" name="flexRadioDefault-v">
                                                <label class="form-check-label" for="flexRadioDefault-v">Bank of America</label>
                                              </div>
                                              <div class="form-check radio radio-primary">
                                                <input class="form-check-input" id="flexRadioDefault-u" type="radio" name="flexRadioDefault-v">
                                                <label class="form-check-label" for="flexRadioDefault-u">HDFC Bank</label>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              </form>
                              <form class="row g-3 needs-validation" novalidate="">   
                                <div class="col-12"> 
                                  <textarea class="form-control" id="validationTextarea24" placeholder="Your Feedback" required=""></textarea>
                                  <div class="invalid-feedback">Please enter a message in the textarea.</div>
                                </div>
                                <div class="col-12">
                                  <div class="form-check mb-0">
                                    <input class="form-check-input" id="invalidCheck67" type="checkbox" value="" required="">
                                    <label class="form-check-label mb-0" for="invalidCheck67">Agree to terms and conditions</label>
                                    <div class="invalid-feedback">You must agree before submitting.</div>
                                  </div>
                                </div>
                                <div class="col-12 text-end"> 
                                  <button class="btn btn-success">Finish</button>
                                </div>
                              </form>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="col-md-12">
                <div class="card">
                  <div class="card-header pb-0">
                    <h4>Shipping form</h4>
                    <p class="f-m-light mt-1">
                       Fill up your true details and next proceed.</p>
                  </div>
                  <div class="card-body">
                    <div class="row shopping-wizard">
                      <div class="col-12"> 
                        <div class="row shipping-form g-5">
                          <div class="col-xl-8 shipping-border">
                            <div class="nav nav-pills horizontal-options shipping-options" id="cart-options-tab" role="tablist" aria-orientation="vertical"><a class="nav-link b-r-0 active" id="bill-wizard-tab" data-bs-toggle="pill" href="#bill-wizard" role="tab" aria-controls="bill-wizard" aria-selected="true"> 
                                <div class="cart-options">
                                  <div class="stroke-icon-wizard"><i class="fa fa-user"></i></div>
                                  <div class="cart-options-content"> 
                                    <h4>Billing</h4>
                                  </div>
                                </div></a><a class="nav-link b-r-0" id="ship-wizard-tab" data-bs-toggle="pill" href="#ship-wizard" role="tab" aria-controls="ship-wizard" aria-selected="false"> 
                                <div class="cart-options">
                                  <div class="stroke-icon-wizard"><i class="fa fa-truck"></i></div>
                                  <div class="cart-options-content"> 
                                    <h4>Shipping</h4>
                                  </div>
                                </div></a><a class="nav-link b-r-0" id="payment-wizard-tab" data-bs-toggle="pill" href="#payment-wizard" role="tab" aria-controls="payment-wizard" aria-selected="false"> 
                                <div class="cart-options">
                                  <div class="stroke-icon-wizard"><i class="fa fa-money"></i></div>
                                  <div class="cart-options-content"> 
                                    <h4>Payment</h4>
                                  </div>
                                </div></a><a class="nav-link b-r-0" id="finish-wizard-tab" data-bs-toggle="pill" href="#finish-wizard" role="tab" aria-controls="finish-wizard" aria-selected="false"> 
                                <div class="cart-options">
                                  <div class="stroke-icon-wizard"><i class="fa fa-check-square"></i></div>
                                  <div class="cart-options-content"> 
                                    <h4>Finish</h4>
                                  </div>
                                </div></a></div>
                            <div class="tab-content dark-field shipping-content" id="cart-options-tabContent">
                              <div class="tab-pane fade show active" id="bill-wizard" role="tabpanel" aria-labelledby="bill-wizard-tab">
                                <h3>Billing Information </h3>
                                <p class="f-light">Fill up the following information </p>
                                <form class="row g-3 needs-validation" novalidate="">
                                  <div class="col-sm-6">
                                    <label class="form-label" for="customFirstname">First Name<span class="txt-danger">*</span></label>
                                    <input class="form-control" id="customFirstname" type="text" placeholder="Enter first name" required="">
                                    <div class="valid-feedback">Looks good!</div>
                                  </div>
                                  <div class="col-sm-6">
                                    <label class="form-label" for="customLastname">Last Name<span class="txt-danger">*</span></label>
                                    <input class="form-control" id="customLastname" type="text" placeholder="Enter last name" required="">
                                    <div class="valid-feedback">Looks good!</div>
                                  </div>
                                  <div class="col-sm-6">
                                    <label class="form-label" for="customContact">Contact Number</label>
                                    <input class="form-control" id="customContact" type="number" placeholder="Enter number" required="">
                                    <div class="valid-feedback">Looks good!</div>
                                  </div>
                                  <div class="col-sm-6">
                                    <label class="form-label" for="customEmail">Email<span class="txt-danger">*</span></label>
                                    <input class="form-control" id="customEmail" type="email" required="" placeholder="<EMAIL>">
                                    <div class="valid-feedback">Looks good!</div>
                                  </div>
                                  <div class="col-12"> 
                                    <label class="form-label" for="exampleFormControlTextarea1">Address </label>
                                    <textarea class="form-control" id="exampleFormControlTextarea1" rows="3"></textarea>
                                  </div>
                                  <div class="col-sm-4">
                                    <label class="form-label" for="customState-wizard">Country</label>
                                    <select class="form-select" id="customState-wizard" required="">
                                      <option selected="" disabled="" value="">Select Country</option>
                                      <option>Africa </option>
                                      <option>India</option>
                                      <option>Indonesia </option>
                                      <option>Netherlands</option>
                                      <option>U.K </option>
                                      <option>U.S</option>
                                    </select>
                                    <div class="invalid-feedback">Please select a valid state.</div>
                                  </div>
                                  <div class="col-sm-4"> 
                                    <label class="form-label" for="customstate">State</label>
                                    <input class="form-control" id="customstate" type="text" placeholder="Enter state" required="">
                                  </div>
                                  <div class="col-sm-4">
                                    <label class="form-label" for="custom-zipcode">Zip Code</label>
                                    <input class="form-control" id="custom-zipcode" type="text" required="">
                                    <div class="invalid-feedback">Please provide a valid zip.</div>
                                  </div>
                                  <div class="col-12">
                                    <div class="form-check">
                                      <input class="form-check-input" id="invalid-check-wizard" type="checkbox" value="" required="">
                                      <label class="form-check-label mb-0 d-block" for="invalid-check-wizard">Remember me for next time</label>
                                      <div class="invalid-feedback">You must agree before submitting.</div>
                                    </div>
                                  </div>
                                  <div class="col-12"> 
                                    <label class="form-label" for="exampleFormControlTextarea-01">Other Notes</label>
                                    <textarea class="form-control" id="exampleFormControlTextarea-01" rows="3" placeholder="Enter your queries..."></textarea>
                                  </div>
                                  <div class="col-12 text-end">
                                    <button class="btn btn-primary">Proceed to Next<i class="fa fa-truck proceed-next pe-2"></i></button>
                                  </div>
                                </form>
                              </div>
                              <div class="tab-pane fade shipping-wizard" id="ship-wizard" role="tabpanel" aria-labelledby="ship-wizard-tab">
                                <h5 class="f-w-600">Shipping Information</h5>
                                <p class="f-light">Fill up the following information to send you the order</p>
                                <div class="shipping-title">
                                  <h6 class="mb-2">Saved Address</h6>
                                  <button class="btn btn-primary" type="button" data-bs-toggle="modal" data-bs-target="#exampleModalgetbootstrap" data-whatever="@getbootstrap"> <i class="fa fa-plus-square f-20"></i></button>
                                  <div class="modal fade" id="exampleModalgetbootstrap" tabindex="-1" role="dialog" aria-labelledby="exampleModalgetbootstrap" aria-hidden="true">
                                    <div class="modal-dialog" role="document">
                                      <div class="modal-content">
                                        <div class="modal-header">
                                          <h4 class="f-w-700">Information</h4>
                                          <button class="btn-close py-0" type="button" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                          <form class="row g-3 needs-validation" novalidate="">
                                            <div class="col-12">
                                              <label class="form-label" for="validationCustom01">Name</label>
                                              <input class="form-control" id="validationCustom01" type="text" placeholder="Enter your name" required="">
                                              <div class="valid-feedback">Looks good!</div>
                                            </div>
                                            <div class="col-12">
                                              <label class="form-label" for="validationAddress-a">Address</label>
                                              <textarea class="form-control" id="validationAddress-a" rows="3" placeholder="Enter your address..."></textarea>
                                              <div class="valid-feedback">Looks good!</div>
                                            </div>
                                            <div class="col-12"> 
                                              <label class="form-label w-100" for="addressType-a">Address Type
                                                <select class="form-select" id="addressType-a" required="">
                                                  <option selected="" value="">Home</option>
                                                  <option>Office</option>
                                                </select>
                                              </label>
                                            </div>
                                            <div class="col-12">
                                              <label for="contact">Contact No</label>
                                              <input class="form-control" id="contact" type="number" placeholder="123456789">
                                            </div>
                                          </form>
                                        </div>
                                        <div class="modal-footer">
                                          <button class="btn me-2 btn-light-primary" type="button" data-bs-dismiss="modal">Close </button>
                                          <button class="btn btn-primary m-0" type="button">Save</button>
                                        </div>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <div class="row g-3">
                                  <div class="col-xxl-4 col-sm-6"> 
                                    <div class="card-wrapper border rounded-3 h-100 light-card"> 
                                      <div class="collect-address">
                                        <div class="d-flex gap-2 align-items-center">
                                          <div class="form-check radio radio-primary">
                                            <input class="form-check-input" id="shipping-choose1" type="radio" name="radio1" value="option1" checked="">
                                            <label class="form-check-label mb-0" for="shipping-choose1">Home</label>
                                          </div>
                                        </div>
                                        <div class="card-icon"><i class="fa fa-pencil"></i><i class="fa fa-trash-o"></i></div>
                                      </div>
                                      <div class="shipping-address">
                                        <h4>Filomena Green </h4><span>
                                          Address: 2211 Fruitville Rd, Sarasota, Florida, US 34237</span><span>
                                          Contact: (941) 321-5643</span>
                                      </div>
                                    </div>
                                  </div>
                                  <div class="col-xxl-4 col-sm-6"> 
                                    <div class="card-wrapper border rounded-3 h-100 light-card">
                                      <div class="collect-address">
                                        <div class="d-flex gap-2 align-items-center">
                                          <div class="form-check radio radio-primary">
                                            <input class="form-check-input" id="shipping-choose2" type="radio" name="radio1" value="option1" checked="">
                                            <label class="form-check-label mb-0" for="shipping-choose2">Work</label>
                                          </div>
                                        </div>
                                        <div class="card-icon"><i class="fa fa-pencil"></i><i class="fa fa-trash-o"></i></div>
                                      </div>
                                      <div class="shipping-address">
                                        <h4>Ms. Ila Runte DDS</h4><span>
                                          Address: 1531 E 23rd St S, Independence, Mississippi, US 64055</span><span>
                                          Contact: (816) 252-4500</span>
                                      </div>
                                    </div>
                                  </div>
                                </div>
                                <h5 class="f-w-600 mt-4 mb-2">Shipping Method</h5>
                                <div class="row shipping-method g-3">
                                  <div class="col-sm-6">
                                    <div class="card-wrapper border rounded-3 h-100 light-card">
                                      <div class="form-check radio radio-primary">
                                        <input class="form-check-input" id="shipping-choose3" type="radio" name="radio2" value="option1" checked="">
                                        <label class="form-check-label mb-0" for="shipping-choose3">Standard Delivery - Free</label>
                                      </div>
                                      <p>Estimated 5-7 days shipping</p>
                                    </div>
                                  </div>
                                  <div class="col-sm-6">
                                    <div class="card-wrapper border rounded-3 h-100 light-card">
                                      <div class="form-check radio radio-primary">
                                        <input class="form-check-input" id="shipping-choose4" type="radio" name="radio2" value="option1" checked="">
                                        <label class="form-check-label mb-0" for="shipping-choose4">Express Delivery - $24</label>
                                      </div>
                                      <P>Estimated 1-2 days shipping</P>
                                    </div>
                                  </div>
                                  <div class="col-12 text-end">
                                    <button class="btn btn-primary">Proceed to Next<i class="fa fa-truck proceed-next pe-2"></i></button>
                                  </div>
                                </div>
                              </div>
                              <div class="tab-pane fade shipping-wizard" id="payment-wizard" role="tabpanel" aria-labelledby="payment-wizard-tab">
                                <h3>Payment Information</h3>
                                <p class="f-light">Fill up the following information to send you the order</p>
                                <div class="payment-info-wrapper">
                                  <div class="row shipping-method g-3">
                                    <div class="col-12">
                                      <div class="card-wrapper border rounded-3 light-card">
                                        <div>
                                          <div class="form-check radio radio-primary">
                                            <input class="form-check-input" id="shipping-choose5" type="radio" name="radio3" value="option1" checked="">
                                            <label class="form-check-label mb-0 f-w-600" for="shipping-choose5">Paypal</label>
                                          </div>
                                          <p>You will be taken to the paypal website to finish your transaction safely</p>
                                        </div>
                                        <div> <img src="../assets/images/checkout/paypal.png" alt="paypal"></div>
                                      </div>
                                    </div>
                                    <div class="col-12">
                                      <div class="card-wrapper border rounded-3 pay-info light-card">
                                        <div>
                                          <div>
                                            <div class="form-check radio radio-primary">
                                              <input class="form-check-input" id="shipping-choose6" type="radio" name="radio3" value="option1">
                                              <label class="form-check-label mb-0 f-w-600" for="shipping-choose6">Credit Card</label>
                                            </div>
                                            <P>Transferring money securely through your bank account. Mastercard, Visa, Discover, and Stripe are all accepted</P>
                                          </div>
                                          <div> <img src="../assets/images/forms/credit-card.png" alt="card"></div>
                                        </div>
                                        <form class="row g-3 needs-validation" novalidate="">
                                          <div class="col-md-12"> 
                                            <label class="form-label" for="placeholdername">Card Holder</label>
                                            <input class="form-control" id="placeholdername" type="text" required="" placeholder="Enter card holder name">
                                          </div>
                                          <div class="col-md-4">
                                            <label class="form-label" for="holdernumber">Card Number</label>
                                            <input class="form-control" id="holdernumber" type="text" required="" placeholder="xxxx xxxx xxxx xxxx">
                                            <div class="invalid-feedback">Please enter your valid number</div>
                                            <div class="valid-feedback">
                                               Looks's Good!</div>
                                          </div>
                                          <div class="col-md-4">
                                            <label class="form-label" for="expirationdates">Expiration(MM/YY)</label>
                                            <input class="form-control" id="expirationdates" type="number" required="" placeholder="xx/xx">
                                            <div class="invalid-feedback">Please enter your valid number</div>
                                            <div class="valid-feedback">
                                               Looks's Good!</div>
                                          </div>
                                          <div class="col-md-4">
                                            <label class="form-label" for="cvvNumber-c">CVV</label>
                                            <input class="form-control" id="cvvNumber-c" type="text" required="">
                                            <div class="invalid-feedback">Please enter your valid number</div>
                                            <div class="valid-feedback">
                                               Looks's Good!</div>
                                          </div>
                                          <div class="col-12"> 
                                            <label class="form-label" for="formFile3">Upload Documentation</label>
                                            <input class="form-control" id="formFile3" type="file" aria-label="file example" required="">
                                            <div class="invalid-feedback">Invalid form file selected</div>
                                          </div>
                                          <div class="col-12">
                                            <div class="form-check">
                                              <input class="form-check-input" id="invalidCheck-c" type="checkbox" value="" required="">
                                              <label class="form-check-label" for="invalidCheck-c">All the above information is correct</label>
                                              <div class="invalid-feedback">You must agree before submitting.</div>
                                            </div>
                                          </div>
                                        </form>
                                      </div>
                                    </div>
                                    <div class="col-12">
                                      <div class="card-wrapper border rounded-3 light-card">
                                        <div>
                                          <div class="form-check radio radio-primary">
                                            <input class="form-check-input" id="shipping-choose7" type="radio" name="radio3" value="option1">
                                            <label class="form-check-label mb-0 f-w-600" for="shipping-choose7">Cash On Delivery</label>
                                          </div>
                                          <p>After your order is delivered, make a cash payment</p>
                                        </div>
                                        <div> <img src="../assets/images/forms/delivery.png" alt="delivery"></div>
                                      </div>
                                    </div>
                                    <div class="col-12 text-end">
                                      <button class="btn btn-primary">Proceed to Next<i class="fa fa-truck proceed-next pe-2"></i></button>
                                    </div>
                                  </div>
                                </div>
                              </div>
                              <div class="tab-pane fade shipping-wizard finish-wizard1" id="finish-wizard" role="tabpanel" aria-labelledby="finish-wizard-tab">
                                <div class="order-confirm"><img src="../assets/images/gif/dashboard-8/successful.gif" alt="popper">
                                  <h5>Thank you! Your order is confirmed.</h5>
                                  <p class="mb-0">An email with your order's specifics will be sent to you as order confirmation.</p>
                                  <p class="text-center f-w-600 mt-2">Order ID: <a class="text-decoration-underline" href="javascript:void(0)">GE34598 </a></p>
                                </div>
                              </div>
                            </div>
                          </div>
                          <div class="col-xl-4"> 
                            <div class="shipping-info">
                              <h4 class="f-w-600">Current Cart </h4>
                              <div class="overflow-auto">
                                <table class="table table-striped">
                                  <thead>
                                    <tr>
                                      <th scope="col">Product</th>
                                      <th scope="col">Product Detail </th>
                                      <th scope="col">Price </th>
                                    </tr>
                                  </thead>
                                  <tbody>
                                    <tr>
                                      <td> <img src="../assets/images/product/13.png" alt="t-shirt"></td>
                                      <td> 
                                        <div> 
                                          <h4>Jackets</h4><span>$98 * 2</span>
                                        </div>
                                      </td>
                                      <td>
                                        <p>$400</p>
                                      </td>
                                    </tr>
                                    <tr>
                                      <td> <img src="../assets/images/email-template/4.png" alt="headphone"></td>
                                      <td> 
                                        <div> 
                                          <h4>Realme X50</h4><span>$4*2</span>
                                        </div>
                                      </td>
                                      <td> 
                                        <p>$450</p>
                                      </td>
                                    </tr>
                                    <tr>
                                      <td> <img src="../assets/images/product/2.png" alt="headphone"></td>
                                      <td> 
                                        <div> 
                                          <h4>Shirt </h4><span>$10 * 2 </span>
                                        </div>
                                      </td>
                                      <td> 
                                        <p>$234</p>
                                      </td>
                                    </tr>
                                    <tr>
                                      <td> <img src="../assets/images/dashboard-2/product/2.png" alt="headphone"></td>
                                      <td> 
                                        <div> 
                                          <h4>i Phone</h4><span>$98 * 2 </span>
                                        </div>
                                      </td>
                                      <td> 
                                        <p>$200</p>
                                      </td>
                                    </tr>
                                  </tbody>
                                  <tfoot>
                                    <tr> 
                                      <td>Sub Total :</td>
                                      <td colspan="2">$1284.00 </td>
                                    </tr>
                                    <tr> 
                                      <td>Discount :</td>
                                      <td colspan="2">$20.00</td>
                                    </tr>
                                    <tr> 
                                      <td>Shipping Charge :</td>
                                      <td colspan="2">$100.78</td>
                                    </tr>
                                    <tr> 
                                      <td>Tax :</td>
                                      <td colspan="2">$205.34</td>
                                    </tr>
                                    <tr> 
                                      <td>Total (USD) :</td>
                                      <td colspan="2">$1569.7</td>
                                    </tr>
                                  </tfoot>
                                </table>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                      <!-- Container-fluid Ends-->
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- footer start-->
        <footer class="footer">
          <div class="container-fluid">
            <div class="row">
              <div class="col-md-6 p-0 footer-copyright">
                <p class="mb-0">Copyright 2024 © Zono theme by pixelstrap.</p>
              </div>
              <div class="col-md-6 p-0">
                <p class="heart mb-0">Hand crafted &amp; made with
                  <svg class="footer-icon">
                    <use href="https://admin.pixelstrap.net/zono/assets/svg/icon-sprite.svg#heart"></use>
                  </svg>
                </p>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
    <!-- latest jquery-->
    <script src="../assets/js/jquery.min.js"></script>
    <!-- Bootstrap js-->
    <script src="../assets/js/bootstrap/bootstrap.bundle.min.js"></script>
    <!-- feather icon js-->
    <script src="../assets/js/icons/feather-icon/feather.min.js"></script>
    <script src="../assets/js/icons/feather-icon/feather-icon.js"></script>
    <!-- scrollbar js-->
    <script src="../assets/js/scrollbar/simplebar.js"></script>
    <script src="../assets/js/scrollbar/custom.js"></script>
    <!-- Sidebar jquery-->
    <script src="../assets/js/config.js"></script>
    <!-- Plugins JS start-->
    <script src="../assets/js/sidebar-menu.js"></script>
    <script src="../assets/js/sidebar-pin.js"></script>
    <script src="../assets/js/slick/slick.min.js"></script>
    <script src="../assets/js/slick/slick.js"></script>
    <script src="../assets/js/header-slick.js"></script>
    <script src="../assets/js/form-wizard/form-wizard.js"></script>
    <script src="../assets/js/form-wizard/image-upload.js"></script>
    <script src="../assets/js/height-equal.js"></script>
    <!-- Plugins JS Ends-->
    <!-- Theme js-->
    <script src="../assets/js/script.js"></script>
    <script src="../assets/js/theme-customizer/customizer.js"></script>
    <!-- Plugin used-->
  </body>

<!-- Mirrored from admin.pixelstrap.net/zono/template/form-wizard.html by HTTrack Website Copier/3.x [XR&CO'2014], Thu, 17 Jul 2025 13:40:17 GMT -->
</html>