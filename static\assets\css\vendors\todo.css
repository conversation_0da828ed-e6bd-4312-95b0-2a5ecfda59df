/*====todo css start====*/
.todo .action-box {
  background: transparent;
  height: 20px;
  width: 20px;
  display: inline-block;
  text-align: center;
  cursor: pointer;
  transition: all 300ms ease;
}
.todo .action-box .icon {
  vertical-align: 0;
}
.todo .action-box.completed {
  background: #DC3545;
  border: 1px solid #DC3545;
  border-radius: 4px;
}
.todo .action-box.completed .icon {
  color: #fff;
  font-size: 14px;
}
.todo .action-box.large {
  height: 26px;
  width: 26px;
}
.todo .action-box.large .icon {
  font-size: 16px;
  vertical-align: -4px;
}
.todo .action-box.large .icon .icon-trash {
  color: #2b5f60;
}
.todo .action-box.large .icon .icon-check {
  color: #2b5f60;
}
.todo .todo-list-wrapper {
  width: 100%;
  margin: 0 auto;
  overflow: auto;
  box-sizing: border-box;
}
.todo .todo-list-wrapper #todo-list {
  list-style: none;
  margin: 0;
  padding: 0;
}
.todo .todo-list-wrapper #todo-list li {
  margin: 0;
  padding: 0;
  width: 100%;
}
.todo .todo-list-wrapper #todo-list li:last-child .task-container {
  border-bottom: 0;
  padding-bottom: 0;
}
.todo .todo-list-wrapper #todo-list li .task-container {
  display: flex;
  justify-content: space-between;
  padding: 10px 0;
  width: 100%;
  border-bottom: 1px solid #EDEDED;
  box-sizing: border-box;
  position: relative;
  transition: background 200ms ease;
}
.todo .todo-list-wrapper #todo-list li .task-container .d-flex {
  align-items: center;
}
.todo .todo-list-wrapper #todo-list li .task-container .d-flex div {
  text-align: right;
}
.todo .todo-list-wrapper #todo-list li .task-container .task-label {
  display: table;
  font-size: 16px;
  font-weight: 400;
  vertical-align: middle;
  color: #999999;
  word-break: break-word;
  padding-top: 5px;
}
.todo .todo-list-wrapper #todo-list li .task-container .task-action-btn {
  display: table-cell;
  vertical-align: middle;
  text-align: right;
}
.todo .todo-list-wrapper #todo-list li .task-container .task-action-btn .action-box {
  border: 1px solid transparent;
}
.todo .todo-list-wrapper #todo-list li .task-container .task-action-btn .action-box:hover {
  background: #fff;
  border-radius: 4px;
}
.todo .todo-list-wrapper #todo-list li .task-container .task-action-btn .action-box:hover .icon {
  color: #59667a;
}
.todo .todo-list-wrapper #todo-list li .task-container:hover h4 {
  color: #15202b;
  transition: all 0.3s ease;
}
.todo .todo-list-wrapper #todo-list li.completed .task-container .complete-btn {
  border: 1px solid #2b5f60;
  border-radius: 4px;
}
.todo .todo-list-wrapper #todo-list li.completed .task-container .complete-btn .icon {
  font-weight: bold;
}
.todo .todo-list-wrapper #todo-list li.completed .task-container .complete-btn .icon i {
  color: #2b5f60;
}
.todo .todo-list-wrapper #todo-list li.completed .task-container .complete-btn:hover {
  background: #fff;
  border: 1px solid #2b5f60;
}
.todo .todo-list-wrapper #todo-list li.completed .task-container .complete-btn:hover .icon {
  color: #fff;
}
.todo .todo-list-wrapper #todo-list li.completed .task-container .task-label {
  text-decoration: line-through;
  color: #2b5f60;
}
.todo .todo-list-wrapper #todo-list li.new .task-container {
  animation: taskHighlighter 2000ms linear 1;
}
.todo .todo-list-wrapper .todo-list-header h5 {
  text-align: center;
  color: #efefef;
}
.todo .notification-popup {
  position: fixed;
  top: 100px;
  right: 10px;
  width: 300px;
  display: inline-block;
  background: #2b5f60;
  border: 1px solid #2b5f60;
  color: #fff;
  padding: 20px;
  opacity: 0.9;
  border-radius: 2px;
  box-sizing: border-box;
  transition: all 300ms ease;
}
.todo .notification-popup.success {
  background: #2b5f60;
  border: 1px solid #2b5f60;
}
.todo .notification-popup p {
  margin-top: 0;
  margin-bottom: 0;
  line-height: 1;
}
.todo .notification-popup .task {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  max-width: 100%;
  display: inline-block;
  color: #fff;
  padding: 0 4px;
}
.todo .notification-popup .notification-text {
  font-size: 14px;
  display: inline-block;
  overflow: hidden;
  color: #fff;
}
.todo .notification-popup.hide {
  opacity: 0;
  visibility: hidden;
}
@-webkit-keyframes taskHighlighter {
  0% {
    background: rgba(68, 102, 242, 0.5);
  }
  100% {
    background: #fff;
  }
}
@keyframes taskHighlighter {
  0% {
    background: rgba(68, 102, 242, 0.5);
  }
  100% {
    background: #fff;
  }
}
.todo-wrap .card-header.b-bottom {
  border-bottom: 1px solid #EDEDED;
}
.todo-wrap .card-header .todo-list-header .new-task-wrapper input {
  padding: 12px 28px;
  background-color: #F8F8F8;
  border: 1px solid #dee2e6;
}
.todo-wrap .card-header .todo-list-header .add-new-task-btn {
  display: flex;
  align-items: center;
  z-index: 0;
}
.todo-wrap .todo-list-body .assign-name {
  opacity: 0.4;
  font-size: 13px;
  font-weight: 400;
}
.todo-wrap .left-bookmark ul li .iconbg {
  padding: 8px;
  border-radius: 30px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.todo-wrap.email-wrap .email-app-sidebar .main-menu > li button.d-block {
  text-align: left;
}
.todo-wrap.email-wrap .email-app-sidebar .main-menu > li + li {
  padding-top: 12px;
}
.todo-wrap.email-wrap .email-app-sidebar .main-menu > li a {
  padding: 0 16px;
  align-items: center;
}
.todo-wrap.email-wrap .email-app-sidebar .main-menu > li a .badge {
  color: #fff;
  padding: 5px 6px 4px 7px;
}
.todo-wrap.email-wrap .email-app-sidebar .main-menu > li a:hover {
  background-color: transparent;
}
.todo-wrap.email-wrap .email-app-sidebar .main-menu > li a:hover .badge {
  color: #fff;
}
/*====todo css end====*/