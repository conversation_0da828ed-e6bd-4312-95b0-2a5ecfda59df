/*! UIkit 3.9.4 | https://www.getuikit.com | (c) 2014 - 2021 YOOtheme | MIT License */(function(Gt,ve){typeof exports=="object"&&typeof module!="undefined"?module.exports=ve():typeof define=="function"&&define.amd?define("uikit",ve):(Gt=typeof globalThis!="undefined"?globalThis:Gt||self,Gt.UIkit=ve())})(this,function(){"use strict";var Gt=Object.prototype,ve=Gt.hasOwnProperty;function At(t,e){return ve.call(t,e)}var Vr=/\B([A-Z])/g,Bt=mt(function(t){return t.replace(Vr,"-$1").toLowerCase()}),Yr=/-(\w)/g,Xt=mt(function(t){return t.replace(Yr,un)}),We=mt(function(t){return t.length?un(null,t.charAt(0))+t.slice(1):""});function un(t,e){return e?e.toUpperCase():""}var Si=String.prototype,Gr=Si.startsWith||function(t){return this.lastIndexOf(t,0)===0};function vt(t,e){return Gr.call(t,e)}var Xr=Si.endsWith||function(t){return this.substr(-t.length)===t};function gt(t,e){return Xr.call(t,e)}var Ti=Array.prototype,hn=function(t,e){return!!~this.indexOf(t,e)},Kr=Si.includes||hn,Jr=Ti.includes||hn;function w(t,e){return t&&(H(t)?Kr:Jr).call(t,e)}var Zr=Ti.findIndex||function(t){for(var e=arguments,i=0;i<this.length;i++)if(t.call(e[1],this[i],i,this))return i;return-1};function Pt(t,e){return Zr.call(t,e)}var ht=Array.isArray;function ct(t){return typeof t=="function"}function Tt(t){return t!==null&&typeof t=="object"}var Qr=Gt.toString;function Mt(t){return Qr.call(t)==="[object Object]"}function ge(t){return Tt(t)&&t===t.window}function Kt(t){return Ei(t)===9}function Ci(t){return Ei(t)>=1}function Ot(t){return Ei(t)===1}function Ei(t){return!ge(t)&&Tt(t)&&t.nodeType}function pe(t){return typeof t=="boolean"}function H(t){return typeof t=="string"}function Jt(t){return typeof t=="number"}function Ct(t){return Jt(t)||H(t)&&!isNaN(t-parseFloat(t))}function Zt(t){return!(ht(t)?t.length:Tt(t)?Object.keys(t).length:!1)}function Z(t){return t===void 0}function Ii(t){return pe(t)?t:t==="true"||t==="1"||t===""?!0:t==="false"||t==="0"?!1:t}function Nt(t){var e=Number(t);return isNaN(e)?!1:e}function k(t){return parseFloat(t)||0}var cn=Array.from||function(t){return Ti.slice.call(t)};function J(t){return C(t)[0]}function C(t){return t&&(Ci(t)?[t]:cn(t).filter(Ci))||[]}function Ft(t){return ge(t)?t:(t=J(t),t?(Kt(t)?t:t.ownerDocument).defaultView:window)}function _i(t){return t?gt(t,"ms")?k(t):k(t)*1e3:0}function Re(t,e){return t===e||Tt(t)&&Tt(e)&&Object.keys(t).length===Object.keys(e).length&&nt(t,function(i,n){return i===e[n]})}function Ai(t,e,i){return t.replace(new RegExp(e+"|"+i,"g"),function(n){return n===e?i:e})}var S=Object.assign||function(t){for(var e=[],i=arguments.length-1;i-- >0;)e[i]=arguments[i+1];t=Object(t);for(var n=0;n<e.length;n++){var r=e[n];if(r!==null)for(var s in r)At(r,s)&&(t[s]=r[s])}return t};function me(t){return t[t.length-1]}function nt(t,e){for(var i in t)if(e(t[i],i)===!1)return!1;return!0}function we(t,e){return t.slice().sort(function(i,n){var r=i[e];r===void 0&&(r=0);var s=n[e];return s===void 0&&(s=0),r>s?1:s>r?-1:0})}function fn(t,e){var i=new Set;return t.filter(function(n){var r=n[e];return i.has(r)?!1:i.add(r)||!0})}function pt(t,e,i){return e===void 0&&(e=0),i===void 0&&(i=1),Math.min(Math.max(Nt(t)||0,e),i)}function D(){}function ln(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];return[["bottom","top"],["right","left"]].every(function(i){var n=i[0],r=i[1];return Math.min.apply(Math,t.map(function(s){var o=s[n];return o}))-Math.max.apply(Math,t.map(function(s){var o=s[r];return o}))>0})}function je(t,e){return t.x<=e.right&&t.x>=e.left&&t.y<=e.bottom&&t.y>=e.top}var Qt={ratio:function(t,e,i){var n,r=e==="width"?"height":"width";return n={},n[r]=t[e]?Math.round(i*t[r]/t[e]):t[r],n[e]=i,n},contain:function(t,e){var i=this;return t=S({},t),nt(t,function(n,r){return t=t[r]>e[r]?i.ratio(t,r,e[r]):t}),t},cover:function(t,e){var i=this;return t=this.contain(t,e),nt(t,function(n,r){return t=t[r]<e[r]?i.ratio(t,r,e[r]):t}),t}};function Ut(t,e,i,n){i===void 0&&(i=0),n===void 0&&(n=!1),e=C(e);var r=e.length;return t=Ct(t)?Nt(t):t==="next"?i+1:t==="previous"?i-1:e.indexOf(J(t)),n?pt(t,0,r-1):(t%=r,t<0?t+r:t)}function mt(t){var e=Object.create(null);return function(i){return e[i]||(e[i]=t(i))}}function M(t,e,i){if(Tt(e)){for(var n in e)M(t,n,e[n]);return}if(Z(i))return t=J(t),t&&t.getAttribute(e);C(t).forEach(function(r){ct(i)&&(i=i.call(r,M(r,e))),i===null?Pi(r,e):r.setAttribute(e,i)})}function te(t,e){return C(t).some(function(i){return i.hasAttribute(e)})}function Pi(t,e){t=C(t),e.split(" ").forEach(function(i){return t.forEach(function(n){return n.hasAttribute(i)&&n.removeAttribute(i)})})}function ft(t,e){for(var i=0,n=[e,"data-"+e];i<n.length;i++)if(te(t,n[i]))return M(t,n[i])}var lt=typeof window!="undefined",be=lt&&/msie|trident/i.test(window.navigator.userAgent),U=lt&&M(document.documentElement,"dir")==="rtl",ee=lt&&"ontouchstart"in window,ie=lt&&window.PointerEvent,dn=lt&&(ee||window.DocumentTouch&&document instanceof DocumentTouch||navigator.maxTouchPoints),dt=ie?"pointerdown":ee?"touchstart":"mousedown",ne=ie?"pointermove":ee?"touchmove":"mousemove",$t=ie?"pointerup":ee?"touchend":"mouseup",Lt=ie?"pointerenter":ee?"":"mouseenter",re=ie?"pointerleave":ee?"":"mouseleave",se=ie?"pointercancel":"touchcancel",Ur={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,menuitem:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0};function Mi(t){return C(t).some(function(e){return Ur[e.tagName.toLowerCase()]})}function W(t){return C(t).some(function(e){return e.offsetWidth||e.offsetHeight||e.getClientRects().length})}var $e="input,select,textarea,button";function Ni(t){return C(t).some(function(e){return Y(e,$e)})}var qe=$e+",a[href],[tabindex]";function Ve(t){return Y(t,qe)}function z(t){return t=J(t),t&&Ot(t.parentNode)&&t.parentNode}function xe(t,e){return C(t).filter(function(i){return Y(i,e)})}var Ye=lt?Element.prototype:{},ts=Ye.matches||Ye.webkitMatchesSelector||Ye.msMatchesSelector||D;function Y(t,e){return C(t).some(function(i){return ts.call(i,e)})}var es=Ye.closest||function(t){var e=this;do if(Y(e,t))return e;while(e=z(e))};function ot(t,e){return vt(e,">")&&(e=e.slice(1)),Ot(t)?es.call(t,e):C(t).map(function(i){return ot(i,e)}).filter(Boolean)}function G(t,e){return H(e)?Y(t,e)||!!ot(t,e):t===e||(Kt(e)?e.documentElement:J(e)).contains(J(t))}function ye(t,e){for(var i=[];t=z(t);)(!e||Y(t,e))&&i.push(t);return i}function R(t,e){t=J(t);var i=t?C(t.children):[];return e?xe(i,e):i}function oe(t,e){return e?C(t).indexOf(J(e)):R(z(t)).indexOf(t)}function wt(t,e){return Di(t,vn(t,e))}function ke(t,e){return Se(t,vn(t,e))}function vn(t,e){return e===void 0&&(e=document),H(t)&&pn(t)||Kt(e)?e:e.ownerDocument}function Di(t,e){return J(gn(t,e,"querySelector"))}function Se(t,e){return C(gn(t,e,"querySelectorAll"))}function gn(t,e,i){if(e===void 0&&(e=document),!t||!H(t))return t;t=t.replace(ns,"$1 *"),pn(t)&&(t=ss(t).map(function(n){var r=e;if(n[0]==="!"){var s=n.substr(1).trim().split(" ");r=ot(z(e),s[0]),n=s.slice(1).join(" ").trim()}if(n[0]==="-"){var o=n.substr(1).trim().split(" "),a=(r||e).previousElementSibling;r=Y(a,n.substr(1))?a:null,n=o.slice(1).join(" ")}return r?os(r)+" "+n:null}).filter(Boolean).join(","),e=document);try{return e[i](t)}catch{return null}}var is=/(^|[^\\],)\s*[!>+~-]/,ns=/([!>+~-])(?=\s+[!>+~-]|\s*$)/g,pn=mt(function(t){return t.match(is)}),rs=/.*?[^\\](?:,|$)/g,ss=mt(function(t){return t.match(rs).map(function(e){return e.replace(/,$/,"").trim()})});function os(t){for(var e=[];t.parentNode;)if(t.id){e.unshift("#"+Te(t.id));break}else{var i=t.tagName;i!=="HTML"&&(i+=":nth-child("+(oe(t)+1)+")"),e.unshift(i),t=t.parentNode}return e.join(" > ")}var as=lt&&window.CSS&&CSS.escape||function(t){return t.replace(/([^\x7f-\uFFFF\w-])/g,function(e){return"\\"+e})};function Te(t){return H(t)?as.call(null,t):""}function A(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];var i=mn(t),n=i[0],r=i[1],s=i[2],o=i[3],a=i[4];return n=Ge(n),o.length>1&&(o=hs(o)),a&&a.self&&(o=cs(o)),s&&(o=us(s,o)),a=wn(a),r.split(" ").forEach(function(u){return n.forEach(function(c){return c.addEventListener(u,o,a)})}),function(){return Wt(n,r,o,a)}}function Wt(t,e,i,n){n===void 0&&(n=!1),n=wn(n),t=Ge(t),e.split(" ").forEach(function(r){return t.forEach(function(s){return s.removeEventListener(r,i,n)})})}function X(){for(var t=[],e=arguments.length;e--;)t[e]=arguments[e];var i=mn(t),n=i[0],r=i[1],s=i[2],o=i[3],a=i[4],u=i[5],c=A(n,r,s,function(f){var h=!u||u(f);h&&(c(),o(f,h))},a);return c}function m(t,e,i){return Ge(t).reduce(function(n,r){return n&&r.dispatchEvent(Ce(e,!0,!0,i))},!0)}function Ce(t,e,i,n){if(e===void 0&&(e=!0),i===void 0&&(i=!1),H(t)){var r=document.createEvent("CustomEvent");r.initCustomEvent(t,e,i,n),t=r}return t}function mn(t){return ct(t[2])&&t.splice(2,0,!1),t}function us(t,e){var i=this;return function(n){var r=t[0]===">"?Se(t,n.currentTarget).reverse().filter(function(s){return G(n.target,s)})[0]:ot(n.target,t);r&&(n.current=r,e.call(i,n))}}function hs(t){return function(e){return ht(e.detail)?t.apply(void 0,[e].concat(e.detail)):t(e)}}function cs(t){return function(e){if(e.target===e.currentTarget||e.target===e.current)return t.call(null,e)}}function wn(t){return t&&be&&!pe(t)?!!t.capture:t}function bn(t){return t&&"addEventListener"in t}function fs(t){return bn(t)?t:J(t)}function Ge(t){return ht(t)?t.map(fs).filter(Boolean):H(t)?Se(t):bn(t)?[t]:C(t)}function Et(t){return t.pointerType==="touch"||!!t.touches}function ae(t){var e=t.touches,i=t.changedTouches,n=e&&e[0]||i&&i[0]||t,r=n.clientX,s=n.clientY;return{x:r,y:s}}var b=lt&&window.Promise||at,Xe=function(){var t=this;this.promise=new b(function(e,i){t.reject=i,t.resolve=e})},$n=0,xn=1,Ke=2,ls=lt&&window.setImmediate||setTimeout;function at(t){this.state=Ke,this.value=void 0,this.deferred=[];var e=this;try{t(function(i){e.resolve(i)},function(i){e.reject(i)})}catch(i){e.reject(i)}}at.reject=function(t){return new at(function(e,i){i(t)})},at.resolve=function(t){return new at(function(e,i){e(t)})},at.all=function(e){return new at(function(i,n){var r=[],s=0;e.length===0&&i(r);function o(u){return function(c){r[u]=c,s+=1,s===e.length&&i(r)}}for(var a=0;a<e.length;a+=1)at.resolve(e[a]).then(o(a),n)})},at.race=function(e){return new at(function(i,n){for(var r=0;r<e.length;r+=1)at.resolve(e[r]).then(i,n)})};var Ee=at.prototype;Ee.resolve=function(e){var i=this;if(i.state===Ke){if(e===i)throw new TypeError("Promise settled with itself.");var n=!1;try{var r=e&&e.then;if(e!==null&&Tt(e)&&ct(r)){r.call(e,function(s){n||i.resolve(s),n=!0},function(s){n||i.reject(s),n=!0});return}}catch(s){n||i.reject(s);return}i.state=$n,i.value=e,i.notify()}},Ee.reject=function(e){var i=this;if(i.state===Ke){if(e===i)throw new TypeError("Promise settled with itself.");i.state=xn,i.value=e,i.notify()}},Ee.notify=function(){var e=this;ls(function(){if(e.state!==Ke)for(;e.deferred.length;){var i=e.deferred.shift(),n=i[0],r=i[1],s=i[2],o=i[3];try{e.state===$n?ct(n)?s(n.call(void 0,e.value)):s(e.value):e.state===xn&&(ct(r)?s(r.call(void 0,e.value)):o(e.value))}catch(a){o(a)}}})},Ee.then=function(e,i){var n=this;return new at(function(r,s){n.deferred.push([e,i,r,s]),n.notify()})},Ee.catch=function(t){return this.then(void 0,t)};function Je(t,e){var i=S({data:null,method:"GET",headers:{},xhr:new XMLHttpRequest,beforeSend:D,responseType:""},e);return b.resolve().then(function(){return i.beforeSend(i)}).then(function(){return ds(t,i)})}function ds(t,e){return new b(function(i,n){var r=e.xhr;for(var s in e)if(s in r)try{r[s]=e[s]}catch{}r.open(e.method.toUpperCase(),t);for(var o in e.headers)r.setRequestHeader(o,e.headers[o]);A(r,"load",function(){r.status===0||r.status>=200&&r.status<300||r.status===304?(e.responseType==="json"&&H(r.response)&&(r=S(vs(r),{response:JSON.parse(r.response)})),i(r)):n(S(Error(r.statusText),{xhr:r,status:r.status}))}),A(r,"error",function(){return n(S(Error("Network Error"),{xhr:r}))}),A(r,"timeout",function(){return n(S(Error("Network Timeout"),{xhr:r}))}),r.send(e.data)})}function zi(t,e,i){return new b(function(n,r){var s=new Image;s.onerror=function(o){return r(o)},s.onload=function(){return n(s)},i&&(s.sizes=i),e&&(s.srcset=e),s.src=t})}function vs(t){var e={};for(var i in t)e[i]=t[i];return e}function gs(t){if(document.readyState!=="loading"){t();return}var e=A(document,"DOMContentLoaded",function(){e(),t()})}function Hi(t){return t=$(t),t.innerHTML="",t}function Dt(t,e){return t=$(t),Z(e)?t.innerHTML:K(t.hasChildNodes()?Hi(t):t,e)}function ps(t,e){return t=$(t),t.hasChildNodes()?Qe(e,function(i){return t.insertBefore(i,t.firstChild)}):K(t,e)}function K(t,e){return t=$(t),Qe(e,function(i){return t.appendChild(i)})}function Ie(t,e){return t=$(t),Qe(e,function(i){return t.parentNode.insertBefore(i,t)})}function Ze(t,e){return t=$(t),Qe(e,function(i){return t.nextSibling?Ie(t.nextSibling,i):K(t.parentNode,i)})}function Qe(t,e){return t=H(t)?ue(t):t,t?"length"in t?C(t).map(e):e(t):null}function rt(t){C(t).forEach(function(e){return e.parentNode&&e.parentNode.removeChild(e)})}function Ue(t,e){for(e=J(Ie(t,e));e.firstChild;)e=e.firstChild;return K(e,t),e}function yn(t,e){return C(C(t).map(function(i){return i.hasChildNodes?Ue(C(i.childNodes),e):K(i,e)}))}function ti(t){C(t).map(z).filter(function(e,i,n){return n.indexOf(e)===i}).forEach(function(e){Ie(e,e.childNodes),rt(e)})}var ms=/^\s*<(\w+|!)[^>]*>/,ws=/^<(\w+)\s*\/?>(?:<\/\1>)?$/;function ue(t){var e=ws.exec(t);if(e)return document.createElement(e[1]);var i=document.createElement("div");return ms.test(t)?i.insertAdjacentHTML("beforeend",t.trim()):i.textContent=t,i.childNodes.length>1?C(i.childNodes):i.firstChild}function It(t,e){if(!!Ot(t))for(e(t),t=t.firstElementChild;t;){var i=t.nextElementSibling;It(t,e),t=i}}function $(t,e){return kn(t)?J(ue(t)):Di(t,e)}function O(t,e){return kn(t)?C(ue(t)):Se(t,e)}function kn(t){return H(t)&&(t[0]==="<"||t.match(/^\s*</))}function y(t){for(var e=[],i=arguments.length-1;i-- >0;)e[i]=arguments[i+1];Sn(t,e,"add")}function N(t){for(var e=[],i=arguments.length-1;i-- >0;)e[i]=arguments[i+1];Sn(t,e,"remove")}function ei(t,e){M(t,"class",function(i){return(i||"").replace(new RegExp("\\b"+e+"\\b","g"),"")})}function Bi(t){for(var e=[],i=arguments.length-1;i-- >0;)e[i]=arguments[i+1];e[0]&&N(t,e[0]),e[1]&&y(t,e[1])}function E(t,e){var i;i=Oi(e),e=i[0];for(var n=C(t),r=0;r<n.length;r++)if(e&&n[r].classList.contains(e))return!0;return!1}function j(t,e,i){e=Oi(e);for(var n=C(t),r=0;r<n.length;r++)for(var s=n[r].classList,o=0;o<e.length;o++)Z(i)?s.toggle(e[o]):ii.Force?s.toggle(e[o],!!i):s[i?"add":"remove"](e[o])}function Sn(t,e,i){var n;e=e.reduce(function(a,u){return a.concat(Oi(u))},[]);for(var r=C(t),s=function(a){ii.Multiple?(n=r[a].classList)[i].apply(n,e):e.forEach(function(u){return r[a].classList[i](u)})},o=0;o<r.length;o++)s(o)}function Oi(t){return String(t).split(/\s|,/).filter(Boolean)}var ii={get Multiple(){return this.get("Multiple")},get Force(){return this.get("Force")},get:function(t){var e=document.createElement("_"),i=e.classList;return i.add("a","b"),i.toggle("c",!1),ii={Multiple:i.contains("b"),Force:!i.contains("c")},ii[t]}},bs={"animation-iteration-count":!0,"column-count":!0,"fill-opacity":!0,"flex-grow":!0,"flex-shrink":!0,"font-weight":!0,"line-height":!0,opacity:!0,order:!0,orphans:!0,"stroke-dasharray":!0,"stroke-dashoffset":!0,widows:!0,"z-index":!0,zoom:!0};function d(t,e,i,n){return n===void 0&&(n=""),C(t).map(function(r){if(H(e)){if(e=ni(e),Z(i))return Tn(r,e);!i&&!Jt(i)?r.style.removeProperty(e):r.style.setProperty(e,Ct(i)&&!bs[e]?i+"px":i,n)}else if(ht(e)){var s=Fi(r);return e.reduce(function(o,a){return o[a]=s[ni(a)],o},{})}else Tt(e)&&(n=i,nt(e,function(o,a){return d(r,a,o,n)}));return r})[0]}function Fi(t,e){return Ft(t).getComputedStyle(t,e)}function Tn(t,e,i){return Fi(t,i)[e]}var $s=mt(function(t){var e=K(document.documentElement,ue("<div>"));y(e,"uk-"+t);var i=Tn(e,"content",":before");return rt(e),i}),xs=/^\s*(["'])?(.*?)\1\s*$/;function Li(t){return(be?$s(t):Fi(document.documentElement).getPropertyValue("--uk-"+t)).replace(xs,"$2")}var ni=mt(function(t){return ys(t)}),Cn=["webkit","moz","ms"];function ys(t){t=Bt(t);var e=document.documentElement,i=e.style;if(t in i)return t;for(var n=Cn.length,r;n--;)if(r="-"+Cn[n]+"-"+t,r in i)return r}function En(t,e,i,n){return i===void 0&&(i=400),n===void 0&&(n="linear"),b.all(C(t).map(function(r){return new b(function(s,o){for(var a in e){var u=d(r,a);u===""&&d(r,a,u)}var c=setTimeout(function(){return m(r,"transitionend")},i);X(r,"transitionend transitioncanceled",function(f){var h=f.type;clearTimeout(c),N(r,"uk-transition"),d(r,{transitionProperty:"",transitionDuration:"",transitionTimingFunction:""}),h==="transitioncanceled"?o():s(r)},{self:!0}),y(r,"uk-transition"),d(r,S({transitionProperty:Object.keys(e).map(ni).join(","),transitionDuration:i+"ms",transitionTimingFunction:n},e))})}))}var P={start:En,stop:function(t){return m(t,"transitionend"),b.resolve()},cancel:function(t){m(t,"transitioncanceled")},inProgress:function(t){return E(t,"uk-transition")}},_e="uk-animation-";function Wi(t,e,i,n,r){return i===void 0&&(i=200),b.all(C(t).map(function(s){return new b(function(o,a){m(s,"animationcanceled");var u=setTimeout(function(){return m(s,"animationend")},i);X(s,"animationend animationcanceled",function(c){var f=c.type;clearTimeout(u),f==="animationcanceled"?a():o(s),d(s,"animationDuration",""),ei(s,_e+"\\S*")},{self:!0}),d(s,"animationDuration",i+"ms"),y(s,e,_e+(r?"leave":"enter")),vt(e,_e)&&(n&&y(s,"uk-transform-origin-"+n),r&&y(s,_e+"reverse"))})}))}var ks=new RegExp(_e+"(enter|leave)"),xt={in:Wi,out:function(t,e,i,n){return Wi(t,e,i,n,!0)},inProgress:function(t){return ks.test(M(t,"class"))},cancel:function(t){m(t,"animationcanceled")}},zt={width:["left","right"],height:["top","bottom"]};function I(t){var e=Ot(t)?J(t).getBoundingClientRect():{height:q(t),width:Pe(t),top:0,left:0};return{height:e.height,width:e.width,top:e.top,left:e.left,bottom:e.top+e.height,right:e.left+e.width}}function B(t,e){var i=I(t);if(t){var n=Ft(t),r=n.pageYOffset,s=n.pageXOffset,o={height:r,width:s};for(var a in zt)for(var u in zt[a])i[zt[a][u]]+=o[a]}if(!e)return i;var c=d(t,"position");nt(d(t,["left","top"]),function(f,h){return d(t,h,e[h]-i[h]+k(c==="absolute"&&f==="auto"?ri(t)[h]:f))})}function ri(t){for(var e=B(t),i=e.top,n=e.left,r=J(t),s=r.ownerDocument,o=s.body,a=s.documentElement,u=r.offsetParent,c=u||a;c&&(c===o||c===a)&&d(c,"position")==="static";)c=c.parentNode;if(Ot(c)){var f=B(c);i-=f.top+k(d(c,"borderTopWidth")),n-=f.left+k(d(c,"borderLeftWidth"))}return{top:i-k(d(t,"marginTop")),left:n-k(d(t,"marginLeft"))}}function Ae(t){var e=[0,0];t=J(t);do if(e[0]+=t.offsetTop,e[1]+=t.offsetLeft,d(t,"position")==="fixed"){var i=Ft(t);return e[0]+=i.pageYOffset,e[1]+=i.pageXOffset,e}while(t=t.offsetParent);return e}var q=In("height"),Pe=In("width");function In(t){var e=We(t);return function(i,n){if(Z(n)){if(ge(i))return i["inner"+e];if(Kt(i)){var r=i.documentElement;return Math.max(r["offset"+e],r["scroll"+e])}return i=J(i),n=d(i,t),n=n==="auto"?i["offset"+e]:k(n)||0,n-Rt(i,t)}else return d(i,t,!n&&n!==0?"":+n+Rt(i,t)+"px")}}function Rt(t,e,i){return i===void 0&&(i="border-box"),d(t,"boxSizing")===i?zt[e].map(We).reduce(function(n,r){return n+k(d(t,"padding"+r))+k(d(t,"border"+r+"Width"))},0):0}function Me(t){for(var e in zt)for(var i in zt[e])if(zt[e][i]===t)return zt[e][1-i];return t}function yt(t,e,i){return e===void 0&&(e="width"),i===void 0&&(i=window),Ct(t)?+t:gt(t,"vh")?Ri(q(Ft(i)),t):gt(t,"vw")?Ri(Pe(Ft(i)),t):gt(t,"%")?Ri(I(i)[e],t):k(t)}function Ri(t,e){return t*k(e)/100}var F={reads:[],writes:[],read:function(t){return this.reads.push(t),qi(),t},write:function(t){return this.writes.push(t),qi(),t},clear:function(t){An(this.reads,t),An(this.writes,t)},flush:ji};function ji(t){t===void 0&&(t=1),_n(F.reads),_n(F.writes.splice(0)),F.scheduled=!1,(F.reads.length||F.writes.length)&&qi(t+1)}var Ss=4;function qi(t){F.scheduled||(F.scheduled=!0,t&&t<Ss?b.resolve().then(function(){return ji(t)}):requestAnimationFrame(function(){return ji()}))}function _n(t){for(var e;e=t.shift();)try{e()}catch(i){console.error(i)}}function An(t,e){var i=t.indexOf(e);return~i&&t.splice(i,1)}function Vi(){}Vi.prototype={positions:[],init:function(){var t=this;this.positions=[];var e;this.unbind=A(document,"mousemove",function(i){return e=ae(i)}),this.interval=setInterval(function(){!e||(t.positions.push(e),t.positions.length>5&&t.positions.shift())},50)},cancel:function(){this.unbind&&this.unbind(),this.interval&&clearInterval(this.interval)},movesTo:function(t){if(this.positions.length<2)return!1;var e=t.getBoundingClientRect(),i=e.left,n=e.right,r=e.top,s=e.bottom,o=this.positions,a=o[0],u=me(this.positions),c=[a,u];if(je(u,e))return!1;var f=[[{x:i,y:r},{x:n,y:s}],[{x:i,y:s},{x:n,y:r}]];return f.some(function(h){var l=Ts(c,h);return l&&je(l,e)})}};function Ts(t,e){var i=t[0],n=i.x,r=i.y,s=t[1],o=s.x,a=s.y,u=e[0],c=u.x,f=u.y,h=e[1],l=h.x,v=h.y,p=(v-f)*(o-n)-(l-c)*(a-r);if(p===0)return!1;var g=((l-c)*(r-f)-(v-f)*(n-c))/p;return g<0?!1:{x:n+g*(o-n),y:r+g*(a-r)}}var tt={};tt.events=tt.created=tt.beforeConnect=tt.connected=tt.beforeDisconnect=tt.disconnected=tt.destroy=Yi,tt.args=function(t,e){return e!==!1&&Yi(e||t)},tt.update=function(t,e){return we(Yi(t,ct(e)?{read:e}:e),"order")},tt.props=function(t,e){return ht(e)&&(e=e.reduce(function(i,n){return i[n]=String,i},{})),tt.methods(t,e)},tt.computed=tt.methods=function(t,e){return e?t?S({},t,e):e:t},tt.data=function(t,e,i){return i?Pn(t,e,i):e?t?function(n){return Pn(t,e,n)}:e:t};function Pn(t,e,i){return tt.computed(ct(t)?t.call(i,i):t,ct(e)?e.call(i,i):e)}function Yi(t,e){return t=t&&!ht(t)?[t]:t,e?t?t.concat(e):ht(e)?e:[e]:t}function Cs(t,e){return Z(e)?t:e}function he(t,e,i){var n={};if(ct(e)&&(e=e.options),e.extends&&(t=he(t,e.extends,i)),e.mixins)for(var r=0,s=e.mixins.length;r<s;r++)t=he(t,e.mixins[r],i);for(var o in t)u(o);for(var a in e)At(t,a)||u(a);function u(c){n[c]=(tt[c]||Cs)(t[c],e[c],i)}return n}function si(t,e){var i;e===void 0&&(e=[]);try{return t?vt(t,"{")?JSON.parse(t):e.length&&!w(t,":")?(i={},i[e[0]]=t,i):t.split(";").reduce(function(n,r){var s=r.split(/:(.*)/),o=s[0],a=s[1];return o&&!Z(a)&&(n[o.trim()]=a.trim()),n},{}):{}}catch{return{}}}function Mn(t){if(ai(t)&&Gi(t,{func:"playVideo",method:"play"}),oi(t))try{t.play().catch(D)}catch{}}function Nn(t){ai(t)&&Gi(t,{func:"pauseVideo",method:"pause"}),oi(t)&&t.pause()}function Dn(t){ai(t)&&Gi(t,{func:"mute",method:"setVolume",value:0}),oi(t)&&(t.muted=!0)}function zn(t){return oi(t)||ai(t)}function oi(t){return t&&t.tagName==="VIDEO"}function ai(t){return t&&t.tagName==="IFRAME"&&(Hn(t)||Bn(t))}function Hn(t){return!!t.src.match(/\/\/.*?youtube(-nocookie)?\.[a-z]+\/(watch\?v=[^&\s]+|embed)|youtu\.be\/.*/)}function Bn(t){return!!t.src.match(/vimeo\.com\/video\/.*/)}function Gi(t,e){Is(t).then(function(){return On(t,e)})}function On(t,e){try{t.contentWindow.postMessage(JSON.stringify(S({event:"command"},e)),"*")}catch{}}var Xi="_ukPlayer",Es=0;function Is(t){if(t[Xi])return t[Xi];var e=Hn(t),i=Bn(t),n=++Es,r;return t[Xi]=new b(function(s){e&&X(t,"load",function(){var o=function(){return On(t,{event:"listening",id:n})};r=setInterval(o,100),o()}),X(window,"message",s,!1,function(o){var a=o.data;try{return a=JSON.parse(a),a&&(e&&a.id===n&&a.event==="onReady"||i&&Number(a.player_id)===n)}catch{}}),t.src=""+t.src+(w(t.src,"?")?"&":"?")+(e?"enablejsapi=1":"api=1&player_id="+n)}).then(function(){return clearInterval(r)})}function ce(t,e,i){return e===void 0&&(e=0),i===void 0&&(i=0),W(t)?ln.apply(void 0,jt(t).map(function(n){var r=B(fe(n)),s=r.top,o=r.left,a=r.bottom,u=r.right;return{top:s-e,left:o-i,bottom:a+e,right:u+i}}).concat(B(t))):!1}function ui(t,e){ge(t)||Kt(t)?t=hi(t):t=J(t),t.scrollTop=e}function Ki(t,e){e===void 0&&(e={});var i=e.offset;i===void 0&&(i=0);var n=W(t)?jt(t):[];return n.reduce(function(a,u,c){var f=u.scrollTop,h=u.scrollHeight,l=u.offsetHeight,v=h-Ne(u),p=B(n[c-1]||t),g=p.height,x=p.top,T=Math.ceil(x-B(fe(u)).top-i+f);return i>0&&l<g+i?T+=i:i=0,T>v?(i-=T-v,T=v):T<0&&(i-=T,T=0),function(){return r(u,T-f).then(a)}},function(){return b.resolve()})();function r(a,u){return new b(function(c){var f=a.scrollTop,h=s(Math.abs(u)),l=Date.now();(function v(){var p=o(pt((Date.now()-l)/h));ui(a,f+u*p),p!==1?requestAnimationFrame(v):c()})()})}function s(a){return 40*Math.pow(a,.375)}function o(a){return .5*(1-Math.cos(Math.PI*a))}}function Ji(t,e){if(e===void 0&&(e=0),!W(t))return 0;var i=jt(t,/auto|scroll/,!0),n=i[0],r=n.scrollHeight,s=n.scrollTop,o=Ne(n),a=Ae(t)[0]-s-Ae(n)[0],u=Math.min(o,a+s),c=a-u,f=Math.min(t.offsetHeight+e+u,r-(a+s),r-o);return pt(-1*c/f)}function jt(t,e,i){e===void 0&&(e=/auto|scroll|hidden/),i===void 0&&(i=!1);var n=hi(t),r=ye(t).reverse();r=r.slice(r.indexOf(n)+1);var s=Pt(r,function(o){return d(o,"position")==="fixed"});return~s&&(r=r.slice(s)),[n].concat(r.filter(function(o){return e.test(d(o,"overflow"))&&(!i||o.scrollHeight>Ne(o))})).reverse()}function fe(t){return t===hi(t)?window:t}function Ne(t){return(t===hi(t)?document.documentElement:t).clientHeight}function hi(t){var e=Ft(t),i=e.document;return i.scrollingElement||i.documentElement}var De={width:["x","left","right"],height:["y","top","bottom"]};function Fn(t,e,i,n,r,s,o,a){i=Wn(i),n=Wn(n);var u={element:i,target:n};if(!t||!e)return u;var c=B(t),f=B(e),h=f;if(Ln(h,i,c,-1),Ln(h,n,f,1),r=Rn(r,c.width,c.height),s=Rn(s,f.width,f.height),r.x+=s.x,r.y+=s.y,h.left+=r.x,h.top+=r.y,o){var l=jt(t).map(fe);a&&!w(l,a)&&l.unshift(a),l=l.map(function(v){return B(v)}),nt(De,function(v,p){var g=v[0],x=v[1],T=v[2];!(o===!0||w(o,g))||l.some(function(L){var _=i[g]===x?-c[p]:i[g]===T?c[p]:0,St=n[g]===x?f[p]:n[g]===T?-f[p]:0;if(h[x]<L[x]||h[x]+c[p]>L[T]){var ut=c[p]/2,bt=n[g]==="center"?-f[p]/2:0;return i[g]==="center"&&(Fe(ut,bt)||Fe(-ut,-bt))||Fe(_,St)}function Fe(Le,ki){var Yt=k((h[x]+Le+ki-r[g]*2).toFixed(4));if(Yt>=L[x]&&Yt+c[p]<=L[T])return h[x]=Yt,["element","target"].forEach(function(an){u[an][g]=Le?u[an][g]===De[p][1]?De[p][2]:De[p][1]:u[an][g]}),!0}})})}return B(t,h),u}function Ln(t,e,i,n){nt(De,function(r,s){var o=r[0],a=r[1],u=r[2];e[o]===u?t[a]+=i[s]*n:e[o]==="center"&&(t[a]+=i[s]*n/2)})}function Wn(t){var e=/left|center|right/,i=/top|center|bottom/;return t=(t||"").split(" "),t.length===1&&(t=e.test(t[0])?t.concat("center"):i.test(t[0])?["center"].concat(t):["center","center"]),{x:e.test(t[0])?t[0]:"center",y:i.test(t[1])?t[1]:"center"}}function Rn(t,e,i){var n=(t||"").split(" "),r=n[0],s=n[1];return{x:r?k(r)*(gt(r,"%")?e/100:1):0,y:s?k(s)*(gt(s,"%")?i/100:1):0}}var _s=Object.freeze({__proto__:null,ajax:Je,getImage:zi,transition:En,Transition:P,animate:Wi,Animation:xt,attr:M,hasAttr:te,removeAttr:Pi,data:ft,addClass:y,removeClass:N,removeClasses:ei,replaceClass:Bi,hasClass:E,toggleClass:j,dimensions:I,offset:B,position:ri,offsetPosition:Ae,height:q,width:Pe,boxModelAdjust:Rt,flipPosition:Me,toPx:yt,ready:gs,empty:Hi,html:Dt,prepend:ps,append:K,before:Ie,after:Ze,remove:rt,wrapAll:Ue,wrapInner:yn,unwrap:ti,fragment:ue,apply:It,$,$$:O,inBrowser:lt,isIE:be,isRtl:U,hasTouch:dn,pointerDown:dt,pointerMove:ne,pointerUp:$t,pointerEnter:Lt,pointerLeave:re,pointerCancel:se,on:A,off:Wt,once:X,trigger:m,createEvent:Ce,toEventTargets:Ge,isTouch:Et,getEventPos:ae,fastdom:F,isVoidElement:Mi,isVisible:W,selInput:$e,isInput:Ni,selFocusable:qe,isFocusable:Ve,parent:z,filter:xe,matches:Y,closest:ot,within:G,parents:ye,children:R,index:oe,hasOwn:At,hyphenate:Bt,camelize:Xt,ucfirst:We,startsWith:vt,endsWith:gt,includes:w,findIndex:Pt,isArray:ht,isFunction:ct,isObject:Tt,isPlainObject:Mt,isWindow:ge,isDocument:Kt,isNode:Ci,isElement:Ot,isBoolean:pe,isString:H,isNumber:Jt,isNumeric:Ct,isEmpty:Zt,isUndefined:Z,toBoolean:Ii,toNumber:Nt,toFloat:k,toArray:cn,toNode:J,toNodes:C,toWindow:Ft,toMs:_i,isEqual:Re,swap:Ai,assign:S,last:me,each:nt,sortBy:we,uniqueBy:fn,clamp:pt,noop:D,intersectRect:ln,pointInRect:je,Dimensions:Qt,getIndex:Ut,memoize:mt,MouseTracker:Vi,mergeOptions:he,parseOptions:si,play:Mn,pause:Nn,mute:Dn,isVideo:zn,positionAt:Fn,Promise:b,Deferred:Xe,query:wt,queryAll:ke,find:Di,findAll:Se,escape:Te,css:d,getCssVar:Li,propName:ni,isInView:ce,scrollTop:ui,scrollIntoView:Ki,scrolledOver:Ji,scrollParents:jt,getViewport:fe,getViewportClientHeight:Ne});function As(t){var e=t.data;t.use=function(r){if(!r.installed)return r.call(null,this),r.installed=!0,this},t.mixin=function(r,s){s=(H(s)?t.component(s):s)||this,s.options=he(s.options,r)},t.extend=function(r){r=r||{};var s=this,o=function(u){this._init(u)};return o.prototype=Object.create(s.prototype),o.prototype.constructor=o,o.options=he(s.options,r),o.super=s,o.extend=s.extend,o},t.update=function(r,s){r=r?J(r):document.body,ye(r).reverse().forEach(function(o){return n(o[e],s)}),It(r,function(o){return n(o[e],s)})};var i;Object.defineProperty(t,"container",{get:function(){return i||document.body},set:function(r){i=$(r)}});function n(r,s){if(!!r)for(var o in r)r[o]._connected&&r[o]._callUpdate(s)}}function Ps(t){t.prototype._callHook=function(n){var r=this,s=this.$options[n];s&&s.forEach(function(o){return o.call(r)})},t.prototype._callConnected=function(){this._connected||(this._data={},this._computeds={},this._initProps(),this._callHook("beforeConnect"),this._connected=!0,this._initEvents(),this._initObservers(),this._callHook("connected"),this._callUpdate())},t.prototype._callDisconnected=function(){!this._connected||(this._callHook("beforeDisconnect"),this._disconnectObservers(),this._unbindEvents(),this._callHook("disconnected"),this._connected=!1,delete this._watch)},t.prototype._callUpdate=function(n){var r=this;n===void 0&&(n="update"),!!this._connected&&((n==="update"||n==="resize")&&this._callWatches(),!!this.$options.update&&(this._updates||(this._updates=new Set,F.read(function(){r._connected&&e.call(r,r._updates),delete r._updates})),this._updates.add(n.type||n)))},t.prototype._callWatches=function(){var n=this;if(!this._watch){var r=!At(this,"_watch");this._watch=F.read(function(){n._connected&&i.call(n,r),n._watch=null})}};function e(n){for(var r=this,s=this.$options.update,o=function(u){var c=s[u],f=c.read,h=c.write,l=c.events;if(!(!n.has("update")&&(!l||!l.some(function(p){return n.has(p)})))){var v=void 0;f&&(v=f.call(r,r._data,n),v&&Mt(v)&&S(r._data,v)),h&&v!==!1&&F.write(function(){return h.call(r,r._data,n)})}},a=0;a<s.length;a++)o(a)}function i(n){var r=this,s=r.$options.computed,o=r._computeds;for(var a in s){var u=At(o,a),c=o[a];delete o[a];var f=s[a],h=f.watch,l=f.immediate;h&&(n&&l||u&&!Re(c,this[a]))&&h.call(this,this[a],c)}}}function Ms(t){var e=0;t.prototype._init=function(h){h=h||{},h.data=u(h,this.constructor.options),this.$options=he(this.constructor.options,h,this),this.$el=null,this.$props={},this._uid=e++,this._initData(),this._initMethods(),this._initComputeds(),this._callHook("created"),h.el&&this.$mount(h.el)},t.prototype._initData=function(){var h=this.$options,l=h.data;l===void 0&&(l={});for(var v in l)this.$props[v]=this[v]=l[v]},t.prototype._initMethods=function(){var h=this.$options,l=h.methods;if(l)for(var v in l)this[v]=l[v].bind(this)},t.prototype._initComputeds=function(){var h=this.$options,l=h.computed;if(this._computeds={},l)for(var v in l)n(this,v,l[v])},t.prototype._initProps=function(h){var l;h=h||i(this.$options,this.$name);for(l in h)Z(h[l])||(this.$props[l]=h[l]);var v=[this.$options.computed,this.$options.methods];for(l in this.$props)l in h&&s(v,l)&&(this[l]=this.$props[l])},t.prototype._initEvents=function(){var h=this;this._events=[];var l=this.$options,v=l.events;v&&v.forEach(function(p){if(At(p,"handler"))r(h,p);else for(var g in p)r(h,p[g],g)})},t.prototype._unbindEvents=function(){this._events.forEach(function(h){return h()}),delete this._events},t.prototype._initObservers=function(){this._observers=[c(this),f(this)]},t.prototype._disconnectObservers=function(){this._observers.forEach(function(h){return h&&h.disconnect()})};function i(h,l){var v={},p=h.args;p===void 0&&(p=[]);var g=h.props;g===void 0&&(g={});var x=h.el;if(!g)return v;for(var T in g){var L=Bt(T),_=ft(x,L);Z(_)||(_=g[T]===Boolean&&_===""?!0:o(g[T],_),!(L==="target"&&(!_||vt(_,"_")))&&(v[T]=_))}var St=si(ft(x,l),p);for(var ut in St){var bt=Xt(ut);g[bt]!==void 0&&(v[bt]=o(g[bt],St[ut]))}return v}function n(h,l,v){Object.defineProperty(h,l,{enumerable:!0,get:function(){var p=h._computeds,g=h.$props,x=h.$el;return At(p,l)||(p[l]=(v.get||v).call(h,g,x)),p[l]},set:function(p){var g=h._computeds;g[l]=v.set?v.set.call(h,p):p,Z(g[l])&&delete g[l]}})}function r(h,l,v){Mt(l)||(l={name:v,handler:l});var p=l.name,g=l.el,x=l.handler,T=l.capture,L=l.passive,_=l.delegate,St=l.filter,ut=l.self;if(g=ct(g)?g.call(h):g||h.$el,ht(g)){g.forEach(function(bt){return r(h,S({},l,{el:bt}),v)});return}!g||St&&!St.call(h)||h._events.push(A(g,p,_?H(_)?_:_.call(h):null,H(x)?h[x]:x.bind(h),{passive:L,capture:T,self:ut}))}function s(h,l){return h.every(function(v){return!v||!At(v,l)})}function o(h,l){return h===Boolean?Ii(l):h===Number?Nt(l):h==="list"?a(l):h?h(l):l}function a(h){return ht(h)?h:H(h)?h.split(/,(?![^(]*\))/).map(function(l){return Ct(l)?Nt(l):Ii(l.trim())}):[h]}function u(h,l){var v=h.data,p=l.args,g=l.props;if(g===void 0&&(g={}),v=ht(v)?Zt(p)?void 0:v.slice(0,p.length).reduce(function(T,L,_){return Mt(L)?S(T,L):T[p[_]]=L,T},{}):v,v)for(var x in v)Z(v[x])?delete v[x]:v[x]=g[x]?o(g[x],v[x]):v[x];return v}function c(h){var l=h.$options,v=l.el,p=new MutationObserver(function(){return h.$emit()});return p.observe(v,{childList:!0,subtree:!0}),p}function f(h){var l=h.$name,v=h.$options,p=h.$props,g=v.attrs,x=v.props,T=v.el;if(!(!x||g===!1)){var L=ht(g)?g:Object.keys(x),_=L.map(function(ut){return Bt(ut)}).concat(l),St=new MutationObserver(function(ut){var bt=i(v,l);ut.some(function(Fe){var Le=Fe.attributeName,ki=Le.replace("data-","");return(ki===l?L:[Xt(ki),Xt(Le)]).some(function(Yt){return!Z(bt[Yt])&&bt[Yt]!==p[Yt]})})&&h.$reset()});return St.observe(T,{attributes:!0,attributeFilter:_.concat(_.map(function(ut){return"data-"+ut}))}),St}}}function Ns(t){var e=t.data;t.prototype.$create=function(n,r,s){return t[n](r,s)},t.prototype.$mount=function(n){var r=this.$options,s=r.name;n[e]||(n[e]={}),!n[e][s]&&(n[e][s]=this,this.$el=this.$options.el=this.$options.el||n,G(n,document)&&this._callConnected())},t.prototype.$reset=function(){this._callDisconnected(),this._callConnected()},t.prototype.$destroy=function(n){n===void 0&&(n=!1);var r=this.$options,s=r.el,o=r.name;s&&this._callDisconnected(),this._callHook("destroy"),!(!s||!s[e])&&(delete s[e][o],Zt(s[e])||delete s[e],n&&rt(this.$el))},t.prototype.$emit=function(n){this._callUpdate(n)},t.prototype.$update=function(n,r){n===void 0&&(n=this.$el),t.update(n,r)},t.prototype.$getComponent=t.getComponent;var i=mt(function(n){return t.prefix+Bt(n)});Object.defineProperties(t.prototype,{$container:Object.getOwnPropertyDescriptor(t,"container"),$name:{get:function(){return i(this.$options.name)}}})}function Ds(t){var e=t.data,i={};t.component=function(n,r){var s=Bt(n);if(n=Xt(s),!r)return Mt(i[n])&&(i[n]=t.extend(i[n])),i[n];t[n]=function(a,u){for(var c=arguments.length,f=Array(c);c--;)f[c]=arguments[c];var h=t.component(n);return h.options.functional?new h({data:Mt(a)?a:[].concat(f)}):a?O(a).map(l)[0]:l(a);function l(v){var p=t.getComponent(v,n);if(p)if(u)p.$destroy();else return p;return new h({el:v,data:u})}};var o=Mt(r)?S({},r):r.options;return o.name=n,o.install&&o.install(t,o,n),t._initialized&&!o.functional&&F.read(function(){return t[n]("[uk-"+s+"],[data-uk-"+s+"]")}),i[n]=Mt(r)?o:r},t.getComponents=function(n){return n&&n[e]||{}},t.getComponent=function(n,r){return t.getComponents(n)[r]},t.connect=function(n){if(n[e])for(var r in n[e])n[e][r]._callConnected();for(var s=0;s<n.attributes.length;s++){var o=jn(n.attributes[s].name);o&&o in i&&t[o](n)}},t.disconnect=function(n){for(var r in n[e])n[e][r]._callDisconnected()}}var jn=mt(function(t){return vt(t,"uk-")||vt(t,"data-uk-")?Xt(t.replace("data-uk-","").replace("uk-","")):!1}),et=function(t){this._init(t)};et.util=_s,et.data="__uikit__",et.prefix="uk-",et.options={},et.version="3.9.4",As(et),Ps(et),Ms(et),Ds(et),Ns(et);function zs(t){if(!!lt){var e,i=function(){e||(e=!0,F.write(function(){return e=!1}),t.update(null,"resize"))};A(window,"load resize",i),A(document,"loadedmetadata load",i,!0),"ResizeObserver"in window&&new ResizeObserver(i).observe(document.documentElement);var n;A(window,"scroll",function(s){n||(n=!0,F.write(function(){return n=!1}),t.update(null,s.type))},{passive:!0,capture:!0});var r=0;A(document,"animationstart",function(s){var o=s.target;(d(o,"animationName")||"").match(/^uk-.*(left|right)/)&&(r++,d(document.documentElement,"overflowX","hidden"),setTimeout(function(){--r||d(document.documentElement,"overflowX","")},_i(d(o,"animationDuration"))+100))},!0),A(document,dt,function(s){if(!!Et(s)){var o=ae(s),a="tagName"in s.target?s.target:z(s.target);X(document,$t+" "+se+" scroll",function(u){var c=ae(u),f=c.x,h=c.y;(u.type!=="scroll"&&a&&f&&Math.abs(o.x-f)>100||h&&Math.abs(o.y-h)>100)&&setTimeout(function(){m(a,"swipe"),m(a,"swipe"+Hs(o.x,o.y,f,h))})})}},{passive:!0})}}function Hs(t,e,i,n){return Math.abs(t-i)>=Math.abs(e-n)?t-i>0?"Left":"Right":e-n>0?"Up":"Down"}function Bs(t){var e=t.connect,i=t.disconnect;if(!lt||!window.MutationObserver)return;F.read(function(){document.body&&It(document.body,e),new MutationObserver(function(s){return s.forEach(n)}).observe(document,{childList:!0,subtree:!0}),new MutationObserver(function(s){return s.forEach(r)}).observe(document,{attributes:!0,subtree:!0}),t._initialized=!0});function n(s){for(var o=s.addedNodes,a=s.removedNodes,u=0;u<o.length;u++)It(o[u],e);for(var c=0;c<a.length;c++)It(a[c],i)}function r(s){var o=s.target,a=s.attributeName,u=jn(a);if(!(!u||!(u in t))){if(te(o,a)){t[u](o);return}var c=t.getComponent(o,u);c&&c.$destroy()}}}var it={connected:function(){!E(this.$el,this.$name)&&y(this.$el,this.$name)}},_t={props:{cls:Boolean,animation:"list",duration:Number,origin:String,transition:String},data:{cls:!1,animation:[!1],duration:200,origin:!1,transition:"linear",clsEnter:"uk-togglabe-enter",clsLeave:"uk-togglabe-leave",initProps:{overflow:"",height:"",paddingTop:"",paddingBottom:"",marginTop:"",marginBottom:""},hideProps:{overflow:"hidden",height:0,paddingTop:0,paddingBottom:0,marginTop:0,marginBottom:0}},computed:{hasAnimation:function(t){var e=t.animation;return!!e[0]},hasTransition:function(t){var e=t.animation;return this.hasAnimation&&e[0]===!0}},methods:{toggleElement:function(t,e,i){var n=this;return new b(function(r){return b.all(C(t).map(function(s){var o=pe(e)?e:!n.isToggled(s);if(!m(s,"before"+(o?"show":"hide"),[n]))return b.reject();var a=(ct(i)?i:i===!1||!n.hasAnimation?n._toggle:n.hasTransition?qn(n):Os(n))(s,o),u=o?n.clsEnter:n.clsLeave;y(s,u),m(s,o?"show":"hide",[n]);var c=function(){N(s,u),m(s,o?"shown":"hidden",[n]),n.$update(s)};return a?a.then(c,function(){return N(s,u),b.reject()}):c()})).then(r,D)})},isToggled:function(t){var e;return t===void 0&&(t=this.$el),e=C(t),t=e[0],E(t,this.clsEnter)?!0:E(t,this.clsLeave)?!1:this.cls?E(t,this.cls.split(" ")[0]):W(t)},_toggle:function(t,e){if(!!t){e=Boolean(e);var i;this.cls?(i=w(this.cls," ")||e!==E(t,this.cls),i&&j(t,this.cls,w(this.cls," ")?void 0:e)):(i=e===t.hidden,i&&(t.hidden=!e)),O("[autofocus]",t).some(function(n){return W(n)?n.focus()||!0:n.blur()}),i&&(m(t,"toggled",[e,this]),this.$update(t))}}}};function qn(t){var e=t.isToggled,i=t.duration,n=t.initProps,r=t.hideProps,s=t.transition,o=t._toggle;return function(a,u){var c=P.inProgress(a),f=a.hasChildNodes?k(d(a.firstElementChild,"marginTop"))+k(d(a.lastElementChild,"marginBottom")):0,h=W(a)?q(a)+(c?0:f):0;P.cancel(a),e(a)||o(a,!0),q(a,""),F.flush();var l=q(a)+(c?0:f);return q(a,h),(u?P.start(a,S({},n,{overflow:"hidden",height:l}),Math.round(i*(1-h/l)),s):P.start(a,r,Math.round(i*(h/l)),s).then(function(){return o(a,!1)})).then(function(){return d(a,n)})}}function Os(t){return function(e,i){xt.cancel(e);var n=t.animation,r=t.duration,s=t._toggle;return i?(s(e,!0),xt.in(e,n[0],r,t.origin)):xt.out(e,n[1]||n[0],r,t.origin).then(function(){return s(e,!1)})}}var Vn={mixins:[it,_t],props:{targets:String,active:null,collapsible:Boolean,multiple:Boolean,toggle:String,content:String,transition:String,offset:Number},data:{targets:"> *",active:!1,animation:[!0],collapsible:!0,multiple:!1,clsOpen:"uk-open",toggle:"> .uk-accordion-title",content:"> .uk-accordion-content",transition:"ease",offset:0},computed:{items:{get:function(t,e){var i=t.targets;return O(i,e)},watch:function(t,e){var i=this;if(t.forEach(function(r){return ci($(i.content,r),!E(r,i.clsOpen))}),!(e||E(t,this.clsOpen))){var n=this.active!==!1&&t[Number(this.active)]||!this.collapsible&&t[0];n&&this.toggle(n,!1)}},immediate:!0},toggles:function(t){var e=t.toggle;return this.items.map(function(i){return $(e,i)})}},events:[{name:"click",delegate:function(){return this.targets+" "+this.$props.toggle},handler:function(t){t.preventDefault(),this.toggle(oe(this.toggles,t.current))}}],methods:{toggle:function(t,e){var i=this,n=[this.items[Ut(t,this.items)]],r=xe(this.items,"."+this.clsOpen);!this.multiple&&!w(r,n[0])&&(n=n.concat(r)),!(!this.collapsible&&r.length<2&&!xe(n,":not(."+this.clsOpen+")").length)&&n.forEach(function(s){return i.toggleElement(s,!E(s,i.clsOpen),function(o,a){j(o,i.clsOpen,a),M($(i.$props.toggle,o),"aria-expanded",a);var u=$(""+(o._wrapper?"> * ":"")+i.content,o);if(e===!1||!i.hasTransition){ci(u,!a);return}return o._wrapper||(o._wrapper=Ue(u,"<div"+(a?" hidden":"")+">")),ci(u,!1),qn(i)(o._wrapper,a).then(function(){if(ci(u,!a),delete o._wrapper,ti(u),a){var c=$(i.$props.toggle,o);ce(c)||Ki(c,{offset:i.offset})}})})})}}};function ci(t,e){t&&(t.hidden=e)}var Fs={mixins:[it,_t],args:"animation",props:{close:String},data:{animation:[!0],selClose:".uk-alert-close",duration:150,hideProps:S({opacity:0},_t.data.hideProps)},events:[{name:"click",delegate:function(){return this.selClose},handler:function(t){t.preventDefault(),this.close()}}],methods:{close:function(){var t=this;this.toggleElement(this.$el).then(function(){return t.$destroy(!0)})}}},Yn={args:"autoplay",props:{automute:Boolean,autoplay:Boolean},data:{automute:!1,autoplay:!0},computed:{inView:function(t){var e=t.autoplay;return e==="inview"}},connected:function(){this.inView&&!te(this.$el,"preload")&&(this.$el.preload="none"),this.automute&&Dn(this.$el)},update:{read:function(){return zn(this.$el)?{visible:W(this.$el)&&d(this.$el,"visibility")!=="hidden",inView:this.inView&&ce(this.$el)}:!1},write:function(t){var e=t.visible,i=t.inView;!e||this.inView&&!i?Nn(this.$el):(this.autoplay===!0||this.inView&&i)&&Mn(this.$el)},events:["resize","scroll"]}},Ls={mixins:[it,Yn],props:{width:Number,height:Number},data:{automute:!0},update:{read:function(){var t=this.$el,e=Ws(t)||z(t),i=e.offsetHeight,n=e.offsetWidth,r=Qt.cover({width:this.width||t.naturalWidth||t.videoWidth||t.clientWidth,height:this.height||t.naturalHeight||t.videoHeight||t.clientHeight},{width:n+(n%2?1:0),height:i+(i%2?1:0)});return!r.width||!r.height?!1:r},write:function(t){var e=t.height,i=t.width;d(this.$el,{height:e,width:i})},events:["resize"]}};function Ws(t){for(;t=z(t);)if(d(t,"position")!=="static")return t}var le={props:{container:Boolean},data:{container:!0},computed:{container:function(t){var e=t.container;return e===!0&&this.$container||e&&$(e)}}},Gn={props:{pos:String,offset:null,flip:Boolean,clsPos:String},data:{pos:"bottom-"+(U?"right":"left"),flip:!0,offset:!1,clsPos:""},computed:{pos:function(t){var e=t.pos;return(e+(w(e,"-")?"":"-center")).split("-")},dir:function(){return this.pos[0]},align:function(){return this.pos[1]}},methods:{positionAt:function(t,e,i){ei(t,this.clsPos+"-(top|bottom|left|right)(-[a-z]+)?");var n=this,r=n.offset,s=this.getAxis();if(!Ct(r)){var o=$(r);r=o?B(o)[s==="x"?"left":"top"]-B(e)[s==="x"?"right":"bottom"]:0}var a=Fn(t,e,s==="x"?Me(this.dir)+" "+this.align:this.align+" "+Me(this.dir),s==="x"?this.dir+" "+this.align:this.align+" "+this.dir,s==="x"?""+(this.dir==="left"?-r:r):" "+(this.dir==="top"?-r:r),null,this.flip,i).target,u=a.x,c=a.y;this.dir=s==="x"?u:c,this.align=s==="x"?c:u,j(t,this.clsPos+"-"+this.dir+"-"+this.align,this.offset===!1)},getAxis:function(){return this.dir==="top"||this.dir==="bottom"?"y":"x"}}},Q,Xn={mixins:[le,Gn,_t],args:"pos",props:{mode:"list",toggle:Boolean,boundary:Boolean,boundaryAlign:Boolean,delayShow:Number,delayHide:Number,clsDrop:String},data:{mode:["click","hover"],toggle:"- *",boundary:!0,boundaryAlign:!1,delayShow:0,delayHide:800,clsDrop:!1,animation:["uk-animation-fade"],cls:"uk-open",container:!1},computed:{boundary:function(t,e){var i=t.boundary;return i===!0?window:wt(i,e)},clsDrop:function(t){var e=t.clsDrop;return e||"uk-"+this.$options.name},clsPos:function(){return this.clsDrop}},created:function(){this.tracker=new Vi},connected:function(){y(this.$el,this.clsDrop),this.toggle&&!this.target&&(this.target=this.$create("toggle",wt(this.toggle,this.$el),{target:this.$el,mode:this.mode}))},disconnected:function(){this.isActive()&&(Q=null)},events:[{name:"click",delegate:function(){return"."+this.clsDrop+"-close"},handler:function(t){t.preventDefault(),this.hide(!1)}},{name:"click",delegate:function(){return'a[href^="#"]'},handler:function(t){var e=t.defaultPrevented,i=t.current.hash;!e&&i&&!G(i,this.$el)&&this.hide(!1)}},{name:"beforescroll",handler:function(){this.hide(!1)}},{name:"toggle",self:!0,handler:function(t,e){t.preventDefault(),this.isToggled()?this.hide(!1):this.show(e.$el,!1)}},{name:"toggleshow",self:!0,handler:function(t,e){t.preventDefault(),this.show(e.$el)}},{name:"togglehide",self:!0,handler:function(t){t.preventDefault(),Y(this.$el,":focus,:hover")||this.hide()}},{name:Lt+" focusin",filter:function(){return w(this.mode,"hover")},handler:function(t){Et(t)||this.clearTimers()}},{name:re+" focusout",filter:function(){return w(this.mode,"hover")},handler:function(t){!Et(t)&&t.relatedTarget&&this.hide()}},{name:"toggled",self:!0,handler:function(t,e){!e||(this.clearTimers(),this.position())}},{name:"show",self:!0,handler:function(){var t=this;Q=this,this.tracker.init(),X(this.$el,"hide",A(document,dt,function(e){var i=e.target;return!G(i,t.$el)&&X(document,$t+" "+se+" scroll",function(n){var r=n.defaultPrevented,s=n.type,o=n.target;!r&&s===$t&&i===o&&!(t.target&&G(i,t.target))&&t.hide(!1)},!0)}),{self:!0}),X(this.$el,"hide",A(document,"keydown",function(e){e.keyCode===27&&t.hide(!1)}),{self:!0})}},{name:"beforehide",self:!0,handler:function(){this.clearTimers()}},{name:"hide",handler:function(t){var e=t.target;if(this.$el!==e){Q=Q===null&&G(e,this.$el)&&this.isToggled()?this:Q;return}Q=this.isActive()?null:Q,this.tracker.cancel()}}],update:{write:function(){this.isToggled()&&!E(this.$el,this.clsEnter)&&this.position()},events:["resize"]},methods:{show:function(t,e){var i=this;if(t===void 0&&(t=this.target),e===void 0&&(e=!0),this.isToggled()&&t&&this.target&&t!==this.target&&this.hide(!1),this.target=t,this.clearTimers(),!this.isActive()){if(Q){if(e&&Q.isDelaying){this.showTimer=setTimeout(this.show,10);return}for(var n;Q&&n!==Q&&!G(this.$el,Q.$el);)n=Q,Q.hide(!1)}this.container&&z(this.$el)!==this.container&&K(this.container,this.$el),this.showTimer=setTimeout(function(){return i.toggleElement(i.$el,!0)},e&&this.delayShow||0)}},hide:function(t){var e=this;t===void 0&&(t=!0);var i=function(){return e.toggleElement(e.$el,!1,!1)};this.clearTimers(),this.isDelaying=Rs(this.$el).some(function(n){return e.tracker.movesTo(n)}),t&&this.isDelaying?this.hideTimer=setTimeout(this.hide,50):t&&this.delayHide?this.hideTimer=setTimeout(i,this.delayHide):i()},clearTimers:function(){clearTimeout(this.showTimer),clearTimeout(this.hideTimer),this.showTimer=null,this.hideTimer=null,this.isDelaying=!1},isActive:function(){return Q===this},position:function(){N(this.$el,this.clsDrop+"-stack"),j(this.$el,this.clsDrop+"-boundary",this.boundaryAlign);var t=B(this.boundary),e=this.boundaryAlign?t:B(this.target);if(this.align==="justify"){var i=this.getAxis()==="y"?"width":"height";d(this.$el,i,e[i])}else this.boundary&&this.$el.offsetWidth>Math.max(t.right-e.left,e.right-t.left)&&y(this.$el,this.clsDrop+"-stack");this.positionAt(this.$el,this.boundaryAlign?this.boundary:this.target,this.boundary)}}};function Rs(t){var e=[];return It(t,function(i){return d(i,"position")!=="static"&&e.push(i)}),e}var js={mixins:[it],args:"target",props:{target:Boolean},data:{target:!1},computed:{input:function(t,e){return $($e,e)},state:function(){return this.input.nextElementSibling},target:function(t,e){var i=t.target;return i&&(i===!0&&z(this.input)===e&&this.input.nextElementSibling||wt(i,e))}},update:function(){var t=this,e=t.target,i=t.input;if(!!e){var n,r=Ni(e)?"value":"textContent",s=e[r],o=i.files&&i.files[0]?i.files[0].name:Y(i,"select")&&(n=O("option",i).filter(function(a){return a.selected})[0])?n.textContent:i.value;s!==o&&(e[r]=o)}},events:[{name:"change",handler:function(){this.$update()}},{name:"reset",el:function(){return ot(this.$el,"form")},handler:function(){this.$update()}}]},qs={update:{read:function(t){var e=ce(this.$el);if(!e||t.isInView===e)return!1;t.isInView=e},write:function(){this.$el.src=""+this.$el.src},events:["scroll","resize"]}},Kn={props:{margin:String,firstColumn:Boolean},data:{margin:"uk-margin-small-top",firstColumn:"uk-first-column"},update:{read:function(){var t=Zi(this.$el.children);return{rows:t,columns:Vs(t)}},write:function(t){for(var e=t.columns,i=t.rows,n=0;n<i.length;n++)for(var r=0;r<i[n].length;r++)j(i[n][r],this.margin,n!==0),j(i[n][r],this.firstColumn,!!~e[0].indexOf(i[n][r]))},events:["resize"]}};function Zi(t){return Jn(t,"top","bottom")}function Vs(t){for(var e=[],i=0;i<t.length;i++)for(var n=Jn(t[i],"left","right"),r=0;r<n.length;r++)e[r]=e[r]?e[r].concat(n[r]):n[r];return U?e.reverse():e}function Jn(t,e,i){for(var n=[[]],r=0;r<t.length;r++){var s=t[r];if(!!W(s))for(var o=fi(s),a=n.length-1;a>=0;a--){var u=n[a];if(!u[0]){u.push(s);break}var c=void 0;if(u[0].offsetParent===s.offsetParent?c=fi(u[0]):(o=fi(s,!0),c=fi(u[0],!0)),o[e]>=c[i]-1&&o[e]!==c[e]){n.push([s]);break}if(o[i]-1>c[e]||o[e]===c[e]){u.push(s);break}if(a===0){n.unshift([s]);break}}}return n}function fi(t,e){var i;e===void 0&&(e=!1);var n=t.offsetTop,r=t.offsetLeft,s=t.offsetHeight,o=t.offsetWidth;return e&&(i=Ae(t),n=i[0],r=i[1]),{top:n,left:r,bottom:n+s,right:r+o}}var Ys={extends:Kn,mixins:[it],name:"grid",props:{masonry:Boolean,parallax:Number},data:{margin:"uk-grid-margin",clsStack:"uk-grid-stack",masonry:!1,parallax:0},connected:function(){this.masonry&&y(this.$el,"uk-flex-top uk-flex-wrap-top")},update:[{write:function(t){var e=t.columns;j(this.$el,this.clsStack,e.length<2)},events:["resize"]},{read:function(t){var e=t.columns,i=t.rows;if(!e.length||!this.masonry&&!this.parallax||Zn(this.$el))return t.translates=!1,!1;var n=!1,r=R(this.$el),s=Ks(e),o=Xs(r,this.margin)*(i.length-1),a=Math.max.apply(Math,s)+o;this.masonry&&(e=e.map(function(c){return we(c,"offsetTop")}),n=Gs(i,e));var u=Math.abs(this.parallax);return u&&(u=s.reduce(function(c,f,h){return Math.max(c,f+o+(h%2?u:u/8)-a)},0)),{padding:u,columns:e,translates:n,height:n?a:""}},write:function(t){var e=t.height,i=t.padding;d(this.$el,"paddingBottom",i||""),e!==!1&&d(this.$el,"height",e)},events:["resize"]},{read:function(t){var e=t.height;return Zn(this.$el)?!1:{scrolled:this.parallax?Ji(this.$el,e?e-q(this.$el):0)*Math.abs(this.parallax):!1}},write:function(t){var e=t.columns,i=t.scrolled,n=t.translates;i===!1&&!n||e.forEach(function(r,s){return r.forEach(function(o,a){return d(o,"transform",!i&&!n?"":"translateY("+((n&&-n[s][a])+(i?s%2?i:i/8:0))+"px)")})})},events:["scroll","resize"]}]};function Zn(t){return R(t).some(function(e){return d(e,"position")==="absolute"})}function Gs(t,e){var i=t.map(function(n){return Math.max.apply(Math,n.map(function(r){return r.offsetHeight}))});return e.map(function(n){var r=0;return n.map(function(s,o){return r+=o?i[o-1]-n[o-1].offsetHeight:0})})}function Xs(t,e){var i=t.filter(function(r){return E(r,e)}),n=i[0];return k(n?d(n,"marginTop"):d(t[0],"paddingLeft"))}function Ks(t){return t.map(function(e){return e.reduce(function(i,n){return i+n.offsetHeight},0)})}var Qi=be?{props:{selMinHeight:String},data:{selMinHeight:!1,forceHeight:!1},computed:{elements:function(t,e){var i=t.selMinHeight;return i?O(i,e):[e]}},update:[{read:function(){d(this.elements,"height","")},order:-5,events:["resize"]},{write:function(){var t=this;this.elements.forEach(function(e){var i=k(d(e,"minHeight"));i&&(t.forceHeight||Math.round(i+Rt(e,"height","content-box"))>=e.offsetHeight)&&d(e,"height",i)})},order:5,events:["resize"]}]}:{},Js={mixins:[Qi],args:"target",props:{target:String,row:Boolean},data:{target:"> *",row:!0,forceHeight:!0},computed:{elements:function(t,e){var i=t.target;return O(i,e)}},update:{read:function(){return{rows:(this.row?Zi(this.elements):[this.elements]).map(Zs)}},write:function(t){var e=t.rows;e.forEach(function(i){var n=i.heights,r=i.elements;return r.forEach(function(s,o){return d(s,"minHeight",n[o])})})},events:["resize"]}};function Zs(t){if(t.length<2)return{heights:[""],elements:t};var e=t.map(Qn),i=Math.max.apply(Math,e),n=t.some(function(s){return s.style.minHeight}),r=t.some(function(s,o){return!s.style.minHeight&&e[o]<i});return n&&r&&(d(t,"minHeight",""),e=t.map(Qn),i=Math.max.apply(Math,e)),e=t.map(function(s,o){return e[o]===i&&k(s.style.minHeight).toFixed(2)!==i.toFixed(2)?"":i}),{heights:e,elements:t}}function Qn(t){var e=!1;W(t)||(e=t.style.display,d(t,"display","block","important"));var i=I(t).height-Rt(t,"height","content-box");return e!==!1&&d(t,"display",e),i}var Qs={mixins:[Qi],props:{expand:Boolean,offsetTop:Boolean,offsetBottom:Boolean,minHeight:Number},data:{expand:!1,offsetTop:!1,offsetBottom:!1,minHeight:0},update:{read:function(t){var e=t.minHeight;if(!W(this.$el))return!1;var i="",n=Rt(this.$el,"height","content-box");if(this.expand)i=q(window)-(I(document.documentElement).height-I(this.$el).height)-n||"";else{if(i="calc(100vh",this.offsetTop){var r=B(this.$el),s=r.top;i+=s>0&&s<q(window)/2?" - "+s+"px":""}this.offsetBottom===!0?i+=" - "+I(this.$el.nextElementSibling).height+"px":Ct(this.offsetBottom)?i+=" - "+this.offsetBottom+"vh":this.offsetBottom&&gt(this.offsetBottom,"px")?i+=" - "+k(this.offsetBottom)+"px":H(this.offsetBottom)&&(i+=" - "+I(wt(this.offsetBottom,this.$el)).height+"px"),i+=(n?" - "+n+"px":"")+")"}return{minHeight:i,prev:e}},write:function(t){var e=t.minHeight,i=t.prev;d(this.$el,{minHeight:e}),e!==i&&this.$update(this.$el,"resize"),this.minHeight&&k(d(this.$el,"minHeight"))<this.minHeight&&d(this.$el,"minHeight",this.minHeight)},events:["resize"]}},Un={args:"src",props:{id:Boolean,icon:String,src:String,style:String,width:Number,height:Number,ratio:Number,class:String,strokeAnimation:Boolean,focusable:Boolean,attributes:"list"},data:{ratio:1,include:["style","class","focusable"],class:"",strokeAnimation:!1},beforeConnect:function(){this.class+=" uk-svg"},connected:function(){var t=this,e;!this.icon&&w(this.src,"#")&&(e=this.src.split("#"),this.src=e[0],this.icon=e[1]),this.svg=this.getSvg().then(function(i){if(t._connected){var n=no(i,t.$el);return t.svgEl&&n!==t.svgEl&&rt(t.svgEl),t.applyAttributes(n,i),t.$emit(),t.svgEl=n}},D)},disconnected:function(){var t=this;this.svg.then(function(e){t._connected||(Mi(t.$el)&&(t.$el.hidden=!1),rt(e),t.svgEl=null)}),this.svg=null},update:{read:function(){return!!(this.strokeAnimation&&this.svgEl&&W(this.svgEl))},write:function(){io(this.svgEl)},type:["resize"]},methods:{getSvg:function(){var t=this;return Us(this.src).then(function(e){return to(e,t.icon)||b.reject("SVG not found.")})},applyAttributes:function(t,e){var i=this;for(var n in this.$options.props)w(this.include,n)&&n in this&&M(t,n,this[n]);for(var r in this.attributes){var s=this.attributes[r].split(":",2),o=s[0],a=s[1];M(t,o,a)}this.id||Pi(t,"id");var u=["width","height"],c=u.map(function(h){return i[h]});c.some(function(h){return h})||(c=u.map(function(h){return M(e,h)}));var f=M(e,"viewBox");f&&!c.some(function(h){return h})&&(c=f.split(" ").slice(2)),c.forEach(function(h,l){return M(t,u[l],k(h)*i.ratio||null)})}}},Us=mt(function(t){return new b(function(e,i){if(!t){i();return}vt(t,"data:")?e(decodeURIComponent(t.split(",")[1])):Je(t).then(function(n){return e(n.response)},function(){return i("SVG not found.")})})});function to(t,e){return e&&w(t,"<symbol")&&(t=eo(t,e)||t),t=$(t.substr(t.indexOf("<svg"))),t&&t.hasChildNodes()&&t}var tr=/<symbol([^]*?id=(['"])(.+?)\2[^]*?<\/)symbol>/g,li={};function eo(t,e){if(!li[t]){li[t]={},tr.lastIndex=0;for(var i;i=tr.exec(t);)li[t][i[3]]='<svg xmlns="http://www.w3.org/2000/svg"'+i[1]+"svg>"}return li[t][e]}function io(t){var e=er(t);e&&t.style.setProperty("--uk-animation-stroke",e)}function er(t){return Math.ceil(Math.max.apply(Math,[0].concat(O("[stroke]",t).map(function(e){try{return e.getTotalLength()}catch{return 0}}))))}function no(t,e){if(Mi(e)||e.tagName==="CANVAS"){e.hidden=!0;var i=e.nextElementSibling;return ir(t,i)?i:Ze(e,t)}var n=e.lastElementChild;return ir(t,n)?n:K(e,t)}function ir(t,e){return nr(t)&&nr(e)&&rr(t)===rr(e)}function nr(t){return t&&t.tagName==="svg"}function rr(t){return(t.innerHTML||new XMLSerializer().serializeToString(t).replace(/<svg.*?>(.*?)<\/svg>/g,"$1")).replace(/\s/g,"")}var ro='<svg width="14" height="14" viewBox="0 0 14 14" xmlns="http://www.w3.org/2000/svg"><line fill="none" stroke="#000" stroke-width="1.1" x1="1" y1="1" x2="13" y2="13"/><line fill="none" stroke="#000" stroke-width="1.1" x1="13" y1="1" x2="1" y2="13"/></svg>',so='<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><line fill="none" stroke="#000" stroke-width="1.4" x1="1" y1="1" x2="19" y2="19"/><line fill="none" stroke="#000" stroke-width="1.4" x1="19" y1="1" x2="1" y2="19"/></svg>',oo='<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect x="9" y="4" width="1" height="11"/><rect x="4" y="9" width="11" height="1"/></svg>',ao='<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><rect y="9" width="20" height="2"/><rect y="3" width="20" height="2"/><rect y="15" width="20" height="2"/></svg>',uo='<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><rect x="19" y="0" width="1" height="40"/><rect x="0" y="19" width="40" height="1"/></svg>',ho='<svg width="7" height="12" viewBox="0 0 7 12" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.2" points="1 1 6 6 1 11"/></svg>',co='<svg width="7" height="12" viewBox="0 0 7 12" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.2" points="6 1 1 6 6 11"/></svg>',fo='<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" stroke-width="1.1" cx="9" cy="9" r="7"/><path fill="none" stroke="#000" stroke-width="1.1" d="M14,14 L18,18 L14,14 Z"/></svg>',lo='<svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" stroke-width="1.8" cx="17.5" cy="17.5" r="16.5"/><line fill="none" stroke="#000" stroke-width="1.8" x1="38" y1="39" x2="29" y2="30"/></svg>',vo='<svg width="24" height="24" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" stroke-width="1.1" cx="10.5" cy="10.5" r="9.5"/><line fill="none" stroke="#000" stroke-width="1.1" x1="23" y1="23" x2="17" y2="17"/></svg>',go='<svg width="14" height="24" viewBox="0 0 14 24" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.4" points="1.225,23 12.775,12 1.225,1 "/></svg>',po='<svg width="25" height="40" viewBox="0 0 25 40" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="2" points="4.002,38.547 22.527,20.024 4,1.5 "/></svg>',mo='<svg width="14" height="24" viewBox="0 0 14 24" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.4" points="12.775,1 1.225,12 12.775,23 "/></svg>',wo='<svg width="25" height="40" viewBox="0 0 25 40" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="2" points="20.527,1.5 2,20.024 20.525,38.547 "/></svg>',bo='<svg width="30" height="30" viewBox="0 0 30 30" xmlns="http://www.w3.org/2000/svg"><circle fill="none" stroke="#000" cx="15" cy="15" r="14"/></svg>',$o='<svg width="18" height="10" viewBox="0 0 18 10" xmlns="http://www.w3.org/2000/svg"><polyline fill="none" stroke="#000" stroke-width="1.2" points="1 9 9 1 17 9 "/></svg>',di={spinner:bo,totop:$o,marker:oo,"close-icon":ro,"close-large":so,"navbar-toggle-icon":ao,"overlay-icon":uo,"pagination-next":ho,"pagination-previous":co,"search-icon":fo,"search-large":lo,"search-navbar":vo,"slidenav-next":go,"slidenav-next-large":po,"slidenav-previous":mo,"slidenav-previous-large":wo},sr={install:So,extends:Un,args:"icon",props:["icon"],data:{include:["focusable"]},isIcon:!0,beforeConnect:function(){y(this.$el,"uk-icon")},methods:{getSvg:function(){var t=To(this.icon);return t?b.resolve(t):b.reject("Icon not found.")}}},kt={args:!1,extends:sr,data:function(t){return{icon:Bt(t.constructor.options.name)}},beforeConnect:function(){y(this.$el,this.$name)}},or={extends:kt,beforeConnect:function(){y(this.$el,"uk-slidenav")},computed:{icon:function(t,e){var i=t.icon;return E(e,"uk-slidenav-large")?i+"-large":i}}},xo={extends:kt,computed:{icon:function(t,e){var i=t.icon;return E(e,"uk-search-icon")&&ye(e,".uk-search-large").length?"search-large":ye(e,".uk-search-navbar").length?"search-navbar":i}}},yo={extends:kt,computed:{icon:function(){return"close-"+(E(this.$el,"uk-close-large")?"large":"icon")}}},ko={extends:kt,connected:function(){var t=this;this.svg.then(function(e){return e&&t.ratio!==1&&d($("circle",e),"strokeWidth",1/t.ratio)})}},vi={};function So(t){t.icon.add=function(e,i){var n,r=H(e)?(n={},n[e]=i,n):e;nt(r,function(s,o){di[o]=s,delete vi[o]}),t._initialized&&It(document.body,function(s){return nt(t.getComponents(s),function(o){o.$options.isIcon&&o.icon in r&&o.$reset()})})}}function To(t){return di[t]?(vi[t]||(vi[t]=$((di[Co(t)]||di[t]).trim())),vi[t].cloneNode(!0)):null}function Co(t){return U?Ai(Ai(t,"left","right"),"previous","next"):t}var Eo={args:"dataSrc",props:{dataSrc:String,dataSrcset:Boolean,sizes:String,width:Number,height:Number,offsetTop:String,offsetLeft:String,target:String},data:{dataSrc:"",dataSrcset:!1,sizes:!1,width:!1,height:!1,offsetTop:"50vh",offsetLeft:"50vw",target:!1},computed:{cacheKey:function(t){var e=t.dataSrc;return this.$name+"."+e},width:function(t){var e=t.width,i=t.dataWidth;return e||i},height:function(t){var e=t.height,i=t.dataHeight;return e||i},sizes:function(t){var e=t.sizes,i=t.dataSizes;return e||i},isImg:function(t,e){return hr(e)},target:{get:function(t){var e=t.target;return[this.$el].concat(ke(e,this.$el))},watch:function(){this.observe()}},offsetTop:function(t){var e=t.offsetTop;return yt(e,"height")},offsetLeft:function(t){var e=t.offsetLeft;return yt(e,"width")}},connected:function(){if(!window.IntersectionObserver){ze(this.$el,this.dataSrc,this.dataSrcset,this.sizes);return}qt[this.cacheKey]?ze(this.$el,qt[this.cacheKey],this.dataSrcset,this.sizes):this.isImg&&this.width&&this.height&&ze(this.$el,Io(this.width,this.height,this.sizes)),this.observer=new IntersectionObserver(this.load,{rootMargin:this.offsetTop+"px "+this.offsetLeft+"px"}),requestAnimationFrame(this.observe)},disconnected:function(){this.observer&&this.observer.disconnect()},update:{read:function(t){var e=this,i=t.image;if(!this.observer||(!i&&document.readyState==="complete"&&this.load(this.observer.takeRecords()),this.isImg))return!1;i&&i.then(function(n){return n&&n.currentSrc!==""&&ze(e.$el,Ui(n))})},write:function(t){if(this.dataSrcset&&window.devicePixelRatio!==1){var e=d(this.$el,"backgroundSize");(e.match(/^(auto\s?)+$/)||k(e)===t.bgSize)&&(t.bgSize=No(this.dataSrcset,this.sizes),d(this.$el,"backgroundSize",t.bgSize+"px"))}},events:["resize"]},methods:{load:function(t){var e=this;!t.some(function(i){return Z(i.isIntersecting)||i.isIntersecting})||(this._data.image=zi(this.dataSrc,this.dataSrcset,this.sizes).then(function(i){return ze(e.$el,Ui(i),i.srcset,i.sizes),qt[e.cacheKey]=Ui(i),i},function(i){return m(e.$el,new i.constructor(i.type,i))}),this.observer.disconnect())},observe:function(){var t=this;this._connected&&!this._data.image&&this.target.forEach(function(e){return t.observer.observe(e)})}}};function ze(t,e,i,n){if(hr(t)){var r=function(o,a){return a&&a!==t[o]&&(t[o]=a)};r("sizes",n),r("srcset",i),r("src",e)}else if(e){var s=!w(t.style.backgroundImage,e);s&&(d(t,"backgroundImage","url("+Te(e)+")"),m(t,Ce("load",!1)))}}function Io(t,e,i){var n;return i&&(n=Qt.ratio({width:t,height:e},"width",yt(ur(i))),t=n.width,e=n.height),'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="'+t+'" height="'+e+'"></svg>'}var ar=/\s*(.*?)\s*(\w+|calc\(.*?\))\s*(?:,|$)/g;function ur(t){var e;for(ar.lastIndex=0;e=ar.exec(t);)if(!e[1]||window.matchMedia(e[1]).matches){e=Po(e[2]);break}return e||"100vw"}var _o=/\d+(?:\w+|%)/g,Ao=/[+-]?(\d+)/g;function Po(t){return vt(t,"calc")?t.slice(5,-1).replace(_o,function(e){return yt(e)}).replace(/ /g,"").match(Ao).reduce(function(e,i){return e+ +i},0):t}var Mo=/\s+\d+w\s*(?:,|$)/g;function No(t,e){var i=yt(ur(e)),n=(t.match(Mo)||[]).map(k).sort(function(r,s){return r-s});return n.filter(function(r){return r>=i})[0]||n.pop()||""}function hr(t){return t.tagName==="IMG"}function Ui(t){return t.currentSrc||t.src}var cr="__test__",qt;try{qt=window.sessionStorage||{},qt[cr]=1,delete qt[cr]}catch{qt={}}var gi={props:{media:Boolean},data:{media:!1},computed:{matchMedia:function(){var t=Do(this.media);return!t||window.matchMedia(t).matches}}};function Do(t){if(H(t)){if(t[0]==="@"){var e="breakpoint-"+t.substr(1);t=k(Li(e))}else if(isNaN(t))return t}return t&&!isNaN(t)?"(min-width: "+t+"px)":!1}var zo={mixins:[it,gi],props:{fill:String},data:{fill:"",clsWrapper:"uk-leader-fill",clsHide:"uk-leader-hide",attrFill:"data-fill"},computed:{fill:function(t){var e=t.fill;return e||Li("leader-fill-content")}},connected:function(){var t;t=yn(this.$el,'<span class="'+this.clsWrapper+'">'),this.wrapper=t[0]},disconnected:function(){ti(this.wrapper.childNodes)},update:{read:function(t){var e=t.changed,i=t.width,n=i;return i=Math.floor(this.$el.offsetWidth/2),{width:i,fill:this.fill,changed:e||n!==i,hide:!this.matchMedia}},write:function(t){j(this.wrapper,this.clsHide,t.hide),t.changed&&(t.changed=!1,M(this.wrapper,this.attrFill,new Array(t.width).join(t.fill)))},events:["resize"]}},st=[],tn={mixins:[it,le,_t],props:{selPanel:String,selClose:String,escClose:Boolean,bgClose:Boolean,stack:Boolean},data:{cls:"uk-open",escClose:!0,bgClose:!0,overlay:!0,stack:!1},computed:{panel:function(t,e){var i=t.selPanel;return $(i,e)},transitionElement:function(){return this.panel},bgClose:function(t){var e=t.bgClose;return e&&this.panel}},beforeDisconnect:function(){w(st,this)&&this.toggleElement(this.$el,!1,!1)},events:[{name:"click",delegate:function(){return this.selClose},handler:function(t){t.preventDefault(),this.hide()}},{name:"toggle",self:!0,handler:function(t,e){t.defaultPrevented||(t.preventDefault(),this.isToggled()===w(st,this)&&this.toggle())}},{name:"beforeshow",self:!0,handler:function(t){if(w(st,this))return!1;!this.stack&&st.length?(b.all(st.map(function(e){return e.hide()})).then(this.show),t.preventDefault()):st.push(this)}},{name:"show",self:!0,handler:function(){var t=this,e=document.documentElement;Pe(window)>e.clientWidth&&this.overlay&&d(document.body,"overflowY","scroll"),this.stack&&d(this.$el,"zIndex",k(d(this.$el,"zIndex"))+st.length),y(e,this.clsPage),this.bgClose&&X(this.$el,"hide",A(document,dt,function(i){var n=i.target;me(st)!==t||t.overlay&&!G(n,t.$el)||G(n,t.panel)||X(document,$t+" "+se+" scroll",function(r){var s=r.defaultPrevented,o=r.type,a=r.target;!s&&o===$t&&n===a&&t.hide()},!0)}),{self:!0}),this.escClose&&X(this.$el,"hide",A(document,"keydown",function(i){i.keyCode===27&&me(st)===t&&t.hide()}),{self:!0})}},{name:"shown",self:!0,handler:function(){Ve(this.$el)||M(this.$el,"tabindex","-1"),$(":focus",this.$el)||this.$el.focus()}},{name:"hidden",self:!0,handler:function(){var t=this;w(st,this)&&st.splice(st.indexOf(this),1),st.length||d(document.body,"overflowY",""),d(this.$el,"zIndex",""),st.some(function(e){return e.clsPage===t.clsPage})||N(document.documentElement,this.clsPage)}}],methods:{toggle:function(){return this.isToggled()?this.hide():this.show()},show:function(){var t=this;return this.container&&z(this.$el)!==this.container?(K(this.container,this.$el),new b(function(e){return requestAnimationFrame(function(){return t.show().then(e)})})):this.toggleElement(this.$el,!0,fr(this))},hide:function(){return this.toggleElement(this.$el,!1,fr(this))}}};function fr(t){var e=t.transitionElement,i=t._toggle;return function(n,r){return new b(function(s,o){return X(n,"show hide",function(){n._reject&&n._reject(),n._reject=o,i(n,r);var a=X(e,"transitionstart",function(){X(e,"transitionend transitioncancel",s,{self:!0}),clearTimeout(u)},{self:!0}),u=setTimeout(function(){a(),s()},_i(d(e,"transitionDuration")))})}).then(function(){return delete n._reject})}}var Ho={install:Bo,mixins:[tn],data:{clsPage:"uk-modal-page",selPanel:".uk-modal-dialog",selClose:".uk-modal-close, .uk-modal-close-default, .uk-modal-close-outside, .uk-modal-close-full"},events:[{name:"show",self:!0,handler:function(){E(this.panel,"uk-margin-auto-vertical")?y(this.$el,"uk-flex"):d(this.$el,"display","block"),q(this.$el)}},{name:"hidden",self:!0,handler:function(){d(this.$el,"display",""),N(this.$el,"uk-flex")}}]};function Bo(t){var e=t.modal;e.dialog=function(n,r){var s=e('<div class="uk-modal"> <div class="uk-modal-dialog">'+n+"</div> </div>",r);return s.show(),A(s.$el,"hidden",function(){return b.resolve().then(function(){return s.$destroy(!0)})},{self:!0}),s},e.alert=function(n,r){return i(function(s){var o=s.labels;return'<div class="uk-modal-body">'+(H(n)?n:Dt(n))+'</div> <div class="uk-modal-footer uk-text-right"> <button class="uk-button uk-button-primary uk-modal-close" autofocus>'+o.ok+"</button> </div>"},r,function(s){return s.resolve()})},e.confirm=function(n,r){return i(function(s){var o=s.labels;return'<form> <div class="uk-modal-body">'+(H(n)?n:Dt(n))+'</div> <div class="uk-modal-footer uk-text-right"> <button class="uk-button uk-button-default uk-modal-close" type="button">'+o.cancel+'</button> <button class="uk-button uk-button-primary" autofocus>'+o.ok+"</button> </div> </form>"},r,function(s){return s.reject()})},e.prompt=function(n,r,s){return i(function(o){var a=o.labels;return'<form class="uk-form-stacked"> <div class="uk-modal-body"> <label>'+(H(n)?n:Dt(n))+'</label> <input class="uk-input" value="'+(r||"")+'" autofocus> </div> <div class="uk-modal-footer uk-text-right"> <button class="uk-button uk-button-default uk-modal-close" type="button">'+a.cancel+'</button> <button class="uk-button uk-button-primary">'+a.ok+"</button> </div> </form>"},s,function(o){return o.resolve(null)},function(o){return $("input",o.$el).value})},e.labels={ok:"Ok",cancel:"Cancel"};function i(n,r,s,o){r=S({bgClose:!1,escClose:!0,labels:e.labels},r);var a=e.dialog(n(r),r),u=new Xe,c=!1;return A(a.$el,"submit","form",function(f){f.preventDefault(),u.resolve(o&&o(a)),c=!0,a.hide()}),A(a.$el,"hide",function(){return!c&&s(u)}),u.promise.dialog=a,u.promise}}var Oo={extends:Vn,data:{targets:"> .uk-parent",toggle:"> a",content:"> ul"}},lr=".uk-navbar-nav > li > a, .uk-navbar-item, .uk-navbar-toggle",Fo={mixins:[it,le,Qi],props:{dropdown:String,mode:"list",align:String,offset:Number,boundary:Boolean,boundaryAlign:Boolean,clsDrop:String,delayShow:Number,delayHide:Number,dropbar:Boolean,dropbarMode:String,dropbarAnchor:Boolean,duration:Number},data:{dropdown:lr,align:U?"right":"left",clsDrop:"uk-navbar-dropdown",mode:void 0,offset:void 0,delayShow:void 0,delayHide:void 0,boundaryAlign:void 0,flip:"x",boundary:!0,dropbar:!1,dropbarMode:"slide",dropbarAnchor:!1,duration:200,forceHeight:!0,selMinHeight:lr,container:!1},computed:{boundary:function(t,e){var i=t.boundary,n=t.boundaryAlign;return i===!0||n?e:i},dropbarAnchor:function(t,e){var i=t.dropbarAnchor;return wt(i,e)},pos:function(t){var e=t.align;return"bottom-"+e},dropbar:{get:function(t){var e=t.dropbar;return e?(e=this._dropbar||wt(e,this.$el)||$("+ .uk-navbar-dropbar",this.$el),e||(this._dropbar=$("<div></div>"))):null},watch:function(t){y(t,"uk-navbar-dropbar")},immediate:!0},dropContainer:function(t,e){return this.container||e},dropdowns:{get:function(t,e){var i=this,n=t.clsDrop,r=O("."+n,e);return this.dropContainer!==e&&O("."+n,this.dropContainer).forEach(function(s){var o=i.getDropdown(s);!w(r,s)&&o&&o.target&&G(o.target,i.$el)&&r.push(s)}),r},watch:function(t){var e=this;this.$create("drop",t.filter(function(i){return!e.getDropdown(i)}),S({},this.$props,{boundary:this.boundary,pos:this.pos,offset:this.dropbar||this.offset}))},immediate:!0},toggles:function(t,e){var i=t.dropdown;return O(i,e)}},disconnected:function(){this.dropbar&&rt(this.dropbar),delete this._dropbar},events:[{name:"mouseover focusin",delegate:function(){return this.dropdown},handler:function(t){var e=t.current,i=this.getActive();i&&w(i.mode,"hover")&&i.target&&!G(i.target,e)&&!i.tracker.movesTo(i.$el)&&i.hide(!1)}},{name:"keydown",delegate:function(){return this.dropdown},handler:function(t){var e=t.current,i=t.keyCode,n=this.getActive();i===Vt.DOWN&&te(e,"aria-expanded")&&(t.preventDefault(),!n||n.target!==e?(e.click(),X(this.dropContainer,"show",function(r){var s=r.target;return vr(s)})):vr(n.$el)),dr(t,this.toggles,n)}},{name:"keydown",el:function(){return this.dropContainer},delegate:function(){return"."+this.clsDrop},handler:function(t){var e=t.current,i=t.keyCode;if(!!w(this.dropdowns,e)){var n=this.getActive(),r=O(qe,e),s=Pt(r,function(o){return Y(o,":focus")});i===Vt.UP&&(t.preventDefault(),s>0&&r[s-1].focus()),i===Vt.DOWN&&(t.preventDefault(),s<r.length-1&&r[s+1].focus()),i===Vt.ESC&&n&&n.target&&n.target.focus(),dr(t,this.toggles,n)}}},{name:"mouseleave",el:function(){return this.dropbar},filter:function(){return this.dropbar},handler:function(){var t=this.getActive();t&&w(t.mode,"hover")&&!this.dropdowns.some(function(e){return Y(e,":hover")})&&t.hide()}},{name:"beforeshow",el:function(){return this.dropContainer},filter:function(){return this.dropbar},handler:function(){z(this.dropbar)||Ze(this.dropbarAnchor||this.$el,this.dropbar)}},{name:"show",el:function(){return this.dropContainer},filter:function(){return this.dropbar},handler:function(t,e){var i=e.$el,n=e.dir;!E(i,this.clsDrop)||(this.dropbarMode==="slide"&&y(this.dropbar,"uk-navbar-dropbar-slide"),this.clsDrop&&y(i,this.clsDrop+"-dropbar"),n==="bottom"&&this.transitionTo(i.offsetHeight+k(d(i,"marginTop"))+k(d(i,"marginBottom")),i))}},{name:"beforehide",el:function(){return this.dropContainer},filter:function(){return this.dropbar},handler:function(t,e){var i=e.$el,n=this.getActive();Y(this.dropbar,":hover")&&n&&n.$el===i&&t.preventDefault()}},{name:"hide",el:function(){return this.dropContainer},filter:function(){return this.dropbar},handler:function(t,e){var i=e.$el;if(!!E(i,this.clsDrop)){var n=this.getActive();(!n||n&&n.$el===i)&&this.transitionTo(0)}}}],methods:{getActive:function(){return Q&&G(Q.target,this.$el)&&Q},transitionTo:function(t,e){var i=this,n=this,r=n.dropbar,s=W(r)?q(r):0;return e=s<t&&e,d(e,"clip","rect(0,"+e.offsetWidth+"px,"+s+"px,0)"),q(r,s),P.cancel([e,r]),b.all([P.start(r,{height:t},this.duration),P.start(e,{clip:"rect(0,"+e.offsetWidth+"px,"+t+"px,0)"},this.duration)]).catch(D).then(function(){d(e,{clip:""}),i.$update(r)})},getDropdown:function(t){return this.$getComponent(t,"drop")||this.$getComponent(t,"dropdown")}}};function dr(t,e,i){var n=t.current,r=t.keyCode,s=i&&i.target||n,o=e.indexOf(s);r===Vt.LEFT&&o>0&&(i&&i.hide(!1),e[o-1].focus()),r===Vt.RIGHT&&o<e.length-1&&(i&&i.hide(!1),e[o+1].focus()),r===Vt.TAB&&(s.focus(),i&&i.hide(!1))}function vr(t){if(!$(":focus",t)){var e=$(qe,t);e&&e.focus()}}var Vt={TAB:9,ESC:27,LEFT:37,UP:38,RIGHT:39,DOWN:40},Lo={mixins:[tn],args:"mode",props:{mode:String,flip:Boolean,overlay:Boolean},data:{mode:"slide",flip:!1,overlay:!1,clsPage:"uk-offcanvas-page",clsContainer:"uk-offcanvas-container",selPanel:".uk-offcanvas-bar",clsFlip:"uk-offcanvas-flip",clsContainerAnimation:"uk-offcanvas-container-animation",clsSidebarAnimation:"uk-offcanvas-bar-animation",clsMode:"uk-offcanvas",clsOverlay:"uk-offcanvas-overlay",selClose:".uk-offcanvas-close",container:!1},computed:{clsFlip:function(t){var e=t.flip,i=t.clsFlip;return e?i:""},clsOverlay:function(t){var e=t.overlay,i=t.clsOverlay;return e?i:""},clsMode:function(t){var e=t.mode,i=t.clsMode;return i+"-"+e},clsSidebarAnimation:function(t){var e=t.mode,i=t.clsSidebarAnimation;return e==="none"||e==="reveal"?"":i},clsContainerAnimation:function(t){var e=t.mode,i=t.clsContainerAnimation;return e!=="push"&&e!=="reveal"?"":i},transitionElement:function(t){var e=t.mode;return e==="reveal"?z(this.panel):this.panel}},update:{read:function(){this.isToggled()&&!W(this.$el)&&this.hide()},events:["resize"]},events:[{name:"click",delegate:function(){return'a[href^="#"]'},handler:function(t){var e=t.current.hash,i=t.defaultPrevented;!i&&e&&$(e,document.body)&&this.hide()}},{name:"touchstart",passive:!0,el:function(){return this.panel},handler:function(t){var e=t.targetTouches;e.length===1&&(this.clientY=e[0].clientY)}},{name:"touchmove",self:!0,passive:!1,filter:function(){return this.overlay},handler:function(t){t.cancelable&&t.preventDefault()}},{name:"touchmove",passive:!1,el:function(){return this.panel},handler:function(t){if(t.targetTouches.length===1){var e=t.targetTouches[0].clientY-this.clientY,i=this.panel,n=i.scrollTop,r=i.scrollHeight,s=i.clientHeight;(s>=r||n===0&&e>0||r-n<=s&&e<0)&&t.cancelable&&t.preventDefault()}}},{name:"show",self:!0,handler:function(){this.mode==="reveal"&&!E(z(this.panel),this.clsMode)&&(Ue(this.panel,"<div>"),y(z(this.panel),this.clsMode)),d(document.documentElement,"overflowY",this.overlay?"hidden":""),y(document.body,this.clsContainer,this.clsFlip),d(document.body,"touch-action","pan-y pinch-zoom"),d(this.$el,"display","block"),y(this.$el,this.clsOverlay),y(this.panel,this.clsSidebarAnimation,this.mode!=="reveal"?this.clsMode:""),q(document.body),y(document.body,this.clsContainerAnimation),this.clsContainerAnimation&&Wo()}},{name:"hide",self:!0,handler:function(){N(document.body,this.clsContainerAnimation),d(document.body,"touch-action","")}},{name:"hidden",self:!0,handler:function(){this.clsContainerAnimation&&Ro(),this.mode==="reveal"&&ti(this.panel),N(this.panel,this.clsSidebarAnimation,this.clsMode),N(this.$el,this.clsOverlay),d(this.$el,"display",""),N(document.body,this.clsContainer,this.clsFlip),d(document.documentElement,"overflowY","")}},{name:"swipeLeft swipeRight",handler:function(t){this.isToggled()&&gt(t.type,"Left")^this.flip&&this.hide()}}]};function Wo(){gr().content+=",user-scalable=0"}function Ro(){var t=gr();t.content=t.content.replace(/,user-scalable=0$/,"")}function gr(){return $('meta[name="viewport"]',document.head)||K(document.head,'<meta name="viewport">')}var jo={mixins:[it],props:{selContainer:String,selContent:String,minHeight:Number},data:{selContainer:".uk-modal",selContent:".uk-modal-dialog",minHeight:150},computed:{container:function(t,e){var i=t.selContainer;return ot(e,i)},content:function(t,e){var i=t.selContent;return ot(e,i)}},connected:function(){d(this.$el,"minHeight",this.minHeight)},update:{read:function(){return!this.content||!this.container||!W(this.$el)?!1:{current:k(d(this.$el,"maxHeight")),max:Math.max(this.minHeight,q(this.container)-(I(this.content).height-q(this.$el)))}},write:function(t){var e=t.current,i=t.max;d(this.$el,"maxHeight",i),Math.round(e)!==Math.round(i)&&m(this.$el,"resize")},events:["resize"]}},qo={props:["width","height"],connected:function(){y(this.$el,"uk-responsive-width")},update:{read:function(){return W(this.$el)&&this.width&&this.height?{width:Pe(z(this.$el)),height:this.height}:!1},write:function(t){q(this.$el,Qt.contain({height:this.height,width:this.width},t).height)},events:["resize"]}},Vo={props:{offset:Number},data:{offset:0},methods:{scrollTo:function(t){var e=this;t=t&&$(t)||document.body,m(this.$el,"beforescroll",[this,t])&&Ki(t,{offset:this.offset}).then(function(){return m(e.$el,"scrolled",[e,t])})}},events:{click:function(t){t.defaultPrevented||(t.preventDefault(),this.scrollTo("#"+Te(decodeURIComponent((this.$el.hash||"").substr(1)))))}}},Ht="_ukScrollspy",Yo={args:"cls",props:{cls:String,target:String,hidden:Boolean,offsetTop:Number,offsetLeft:Number,repeat:Boolean,delay:Number},data:function(){return{cls:!1,target:!1,hidden:!0,offsetTop:0,offsetLeft:0,repeat:!1,delay:0,inViewClass:"uk-scrollspy-inview"}},computed:{elements:{get:function(t,e){var i=t.target;return i?O(i,e):[e]},watch:function(t){this.hidden&&d(xe(t,":not(."+this.inViewClass+")"),"visibility","hidden")},immediate:!0}},disconnected:function(){var t=this;this.elements.forEach(function(e){N(e,t.inViewClass,e[Ht]?e[Ht].cls:""),delete e[Ht]})},update:[{read:function(t){var e=this;if(!t.update)return b.resolve().then(function(){e.$emit(),t.update=!0}),!1;this.elements.forEach(function(i){i[Ht]||(i[Ht]={cls:ft(i,"uk-scrollspy-class")||e.cls}),i[Ht].show=ce(i,e.offsetTop,e.offsetLeft)})},write:function(t){var e=this;this.elements.forEach(function(i){var n=i[Ht];n.show&&!n.inview&&!n.queued?(n.queued=!0,t.promise=(t.promise||b.resolve()).then(function(){return new b(function(r){return setTimeout(r,e.delay)})}).then(function(){e.toggle(i,!0),setTimeout(function(){n.queued=!1,e.$emit()},300)})):!n.show&&n.inview&&!n.queued&&e.repeat&&e.toggle(i,!1)})},events:["scroll","resize"]}],methods:{toggle:function(t,e){var i=t[Ht];i.off&&i.off(),d(t,"visibility",!e&&this.hidden?"hidden":""),j(t,this.inViewClass,e),j(t,i.cls),/\buk-animation-/.test(i.cls)&&(i.off=X(t,"animationcancel animationend",function(){return ei(t,"uk-animation-[\\w-]+")})),m(t,e?"inview":"outview"),i.inview=e,this.$update(t)}}},Go={props:{cls:String,closest:String,scroll:Boolean,overflow:Boolean,offset:Number},data:{cls:"uk-active",closest:!1,scroll:!1,overflow:!0,offset:0},computed:{links:{get:function(t,e){return O('a[href^="#"]',e).filter(function(i){return i.hash})},watch:function(t){this.scroll&&this.$create("scroll",t,{offset:this.offset||0})},immediate:!0},targets:function(){return O(this.links.map(function(t){return Te(t.hash).substr(1)}).join(","))},elements:function(t){var e=t.closest;return ot(this.links,e||"*")}},update:[{read:function(){var t=this,e=this.targets,i=e.length;if(!i||!W(this.$el))return!1;var n=jt(this.targets,/auto|scroll/,!0),r=n[0],s=r.scrollTop,o=r.scrollHeight,a=o-Ne(r),u=!1;return s===a?u=i-1:(this.targets.every(function(c,f){if(B(c).top-B(fe(r)).top-t.offset<=0)return u=f,!0}),u===!1&&this.overflow&&(u=0)),{active:u}},write:function(t){var e=t.active,i=e!==!1&&!E(this.elements[e],this.cls);this.links.forEach(function(n){return n.blur()}),N(this.elements,this.cls),y(this.elements[e],this.cls),i&&m(this.$el,"active",[e,this.elements[e]])},events:["scroll","resize"]}]},Xo={mixins:[it,gi],props:{top:null,bottom:Boolean,offset:String,animation:String,clsActive:String,clsInactive:String,clsFixed:String,clsBelow:String,selTarget:String,widthElement:Boolean,showOnUp:Boolean,targetOffset:Number},data:{top:0,bottom:!1,offset:0,animation:"",clsActive:"uk-active",clsInactive:"",clsFixed:"uk-sticky-fixed",clsBelow:"uk-sticky-below",selTarget:"",widthElement:!1,showOnUp:!1,targetOffset:!1},computed:{offset:function(t){var e=t.offset;return yt(e)},selTarget:function(t,e){var i=t.selTarget;return i&&$(i,e)||e},widthElement:function(t,e){var i=t.widthElement;return wt(i,e)||this.placeholder},isActive:{get:function(){return E(this.selTarget,this.clsActive)},set:function(t){t&&!this.isActive?(Bi(this.selTarget,this.clsInactive,this.clsActive),m(this.$el,"active")):!t&&!E(this.selTarget,this.clsInactive)&&(Bi(this.selTarget,this.clsActive,this.clsInactive),m(this.$el,"inactive"))}}},connected:function(){this.placeholder=$("+ .uk-sticky-placeholder",this.$el)||$('<div class="uk-sticky-placeholder"></div>'),this.isFixed=!1,this.isActive=!1},disconnected:function(){this.isFixed&&(this.hide(),N(this.selTarget,this.clsInactive)),rt(this.placeholder),this.placeholder=null,this.widthElement=null},events:[{name:"load hashchange popstate",el:function(){return window},handler:function(){var t=this;if(!!(this.targetOffset!==!1&&location.hash&&window.pageYOffset>0)){var e=$(location.hash);e&&F.read(function(){var i=B(e),n=i.top,r=B(t.$el).top,s=t.$el.offsetHeight;t.isFixed&&r+s>=n&&r<=n+e.offsetHeight&&ui(window,n-s-(Ct(t.targetOffset)?t.targetOffset:0)-t.offset)})}}}],update:[{read:function(t,e){var i=t.height;if(this.inactive=!this.matchMedia||!W(this.$el),this.inactive)return!1;this.isActive&&e.has("resize")&&(this.hide(),i=this.$el.offsetHeight,this.show()),i=this.isActive?i:this.$el.offsetHeight;var n=this.isFixed?this.placeholder:this.$el;this.topOffset=B(n).top,this.bottomOffset=this.topOffset+i,this.offsetParentTop=B(n.offsetParent).top;var r=pr("bottom",this);return this.top=Math.max(k(pr("top",this)),this.topOffset)-this.offset,this.bottom=r&&r-this.$el.offsetHeight,this.width=I(W(this.widthElement)?this.widthElement:this.$el).width,{height:i,top:Ae(this.placeholder)[0],margins:d(this.$el,["marginTop","marginBottom","marginLeft","marginRight"])}},write:function(t){var e=t.height,i=t.margins,n=this,r=n.placeholder;d(r,S({height:e},i)),G(r,document)||(Ze(this.$el,r),r.hidden=!0),this.isActive=!!this.isActive},events:["resize"]},{read:function(t){var e=t.scroll;return e===void 0&&(e=0),this.scroll=window.pageYOffset,{dir:e<=this.scroll?"down":"up",scroll:this.scroll}},write:function(t,e){var i=this,n=Date.now(),r=e.has("scroll"),s=t.initTimestamp;s===void 0&&(s=0);var o=t.dir,a=t.lastDir,u=t.lastScroll,c=t.scroll,f=t.top;if(t.lastScroll=c,!(c<0||c===u&&r||this.showOnUp&&!r&&!this.isFixed)&&((n-s>300||o!==a)&&(t.initScroll=c,t.initTimestamp=n),t.lastDir=o,!(this.showOnUp&&!this.isFixed&&Math.abs(t.initScroll-c)<=30&&Math.abs(u-c)<=10)))if(this.inactive||c<this.top||this.showOnUp&&(c<=this.top||o==="down"&&r||o==="up"&&!this.isFixed&&c<=this.bottomOffset)){if(!this.isFixed){xt.inProgress(this.$el)&&f>c&&(xt.cancel(this.$el),this.hide());return}this.isFixed=!1,this.animation&&c>this.topOffset?(xt.cancel(this.$el),xt.out(this.$el,this.animation).then(function(){return i.hide()},D)):this.hide()}else this.isFixed?this.update():this.animation?(xt.cancel(this.$el),this.show(),xt.in(this.$el,this.animation).catch(D)):this.show()},events:["resize","scroll"]}],methods:{show:function(){this.isFixed=!0,this.update(),this.placeholder.hidden=!1},hide:function(){this.isActive=!1,N(this.$el,this.clsFixed,this.clsBelow),d(this.$el,{position:"",top:"",width:""}),this.placeholder.hidden=!0},update:function(){var t=this.top!==0||this.scroll>this.top,e=Math.max(0,this.offset),i="fixed";Ct(this.bottom)&&this.scroll>this.bottom-this.offset&&(e=this.bottom-this.offsetParentTop,i="absolute"),d(this.$el,{position:i,top:e+"px",width:this.width}),this.isActive=t,j(this.$el,this.clsBelow,this.scroll>this.bottomOffset),y(this.$el,this.clsFixed)}}};function pr(t,e){var i=e.$props,n=e.$el,r=e[t+"Offset"],s=i[t];if(!!s)return H(s)&&s.match(/^-?\d/)?r+yt(s):B(s===!0?z(n):wt(s,n)).bottom}var mr={mixins:[_t],args:"connect",props:{connect:String,toggle:String,itemNav:String,active:Number,swiping:Boolean},data:{connect:"~.uk-switcher",toggle:"> * > :first-child",itemNav:!1,active:0,swiping:!0,cls:"uk-active",attrItem:"uk-switcher-item"},computed:{connects:{get:function(t,e){var i=t.connect;return ke(i,e)},watch:function(t){var e=this;this.swiping&&d(t,"touch-action","pan-y pinch-zoom");var i=this.index();this.connects.forEach(function(n){return R(n).forEach(function(r,s){return j(r,e.cls,s===i)})})},immediate:!0},toggles:{get:function(t,e){var i=t.toggle;return O(i,e).filter(function(n){return!Y(n,".uk-disabled *, .uk-disabled, [disabled]")})},watch:function(t){var e=this.index();this.show(~e?e:t[this.active]||t[0])},immediate:!0},children:function(){var t=this;return R(this.$el).filter(function(e){return t.toggles.some(function(i){return G(i,e)})})}},events:[{name:"click",delegate:function(){return this.toggle},handler:function(t){t.preventDefault(),this.show(t.current)}},{name:"click",el:function(){return this.connects.concat(this.itemNav?ke(this.itemNav,this.$el):[])},delegate:function(){return"["+this.attrItem+"],[data-"+this.attrItem+"]"},handler:function(t){t.preventDefault(),this.show(ft(t.current,this.attrItem))}},{name:"swipeRight swipeLeft",filter:function(){return this.swiping},el:function(){return this.connects},handler:function(t){var e=t.type;this.show(gt(e,"Left")?"next":"previous")}}],methods:{index:function(){var t=this;return Pt(this.children,function(e){return E(e,t.cls)})},show:function(t){var e=this,i=this.index(),n=Ut(this.children[Ut(t,this.toggles,i)],R(this.$el));i!==n&&(this.children.forEach(function(r,s){j(r,e.cls,n===s),M(e.toggles[s],"aria-expanded",n===s)}),this.connects.forEach(function(r){var s=r.children;return e.toggleElement(C(s).filter(function(o){return E(o,e.cls)}),!1,i>=0).then(function(){return e.toggleElement(s[n],!0,i>=0)})}))}}},Ko={mixins:[it],extends:mr,props:{media:Boolean},data:{media:960,attrItem:"uk-tab-item"},connected:function(){var t=E(this.$el,"uk-tab-left")?"uk-tab-left":E(this.$el,"uk-tab-right")?"uk-tab-right":!1;t&&this.$create("toggle",this.$el,{cls:t,mode:"media",media:this.media})}},Jo={mixins:[gi,_t],args:"target",props:{href:String,target:null,mode:"list",queued:Boolean},data:{href:!1,target:!1,mode:"click",queued:!0},connected:function(){!w(this.mode,"media")&&!Ve(this.$el)&&M(this.$el,"tabindex","0")},computed:{target:{get:function(t,e){var i=t.href,n=t.target;return n=ke(n||i,e),n.length&&n||[e]},watch:function(){this.updateAria()},immediate:!0}},events:[{name:dt,filter:function(){return w(this.mode,"hover")},handler:function(t){var e=this;!Et(t)||this._showState||(m(this.$el,"focus"),X(document,dt,function(){return m(e.$el,"blur")},!0,function(i){return!G(i.target,e.$el)}),w(this.mode,"click")&&(this._preventClick=!0))}},{name:Lt+" "+re+" focus blur",filter:function(){return w(this.mode,"hover")},handler:function(t){if(!Et(t)){var e=w([Lt,"focus"],t.type),i=M(this.$el,"aria-expanded");if(!(!e&&(t.type===re&&Y(this.$el,":focus")||t.type==="blur"&&Y(this.$el,":hover")))){if(this._showState&&e===(i!==this._showState)){e||(this._showState=null);return}this._showState=e?i:null,this.toggle("toggle"+(e?"show":"hide"))}}}},{name:"keydown",filter:function(){return w(this.mode,"click")},handler:function(t){t.keyCode===32&&(t.preventDefault(),this.$el.click())}},{name:"click",filter:function(){return w(this.mode,"click")},handler:function(t){if(this._preventClick)return this._preventClick=null;var e;(ot(t.target,'a[href="#"], a[href=""]')||(e=ot(t.target,"a[href]"))&&(M(this.$el,"aria-expanded")!=="true"||e.hash&&Y(this.target,e.hash)))&&t.preventDefault(),this.toggle()}},{name:"toggled",self:!0,el:function(){return this.target},handler:function(t,e){t.target===this.target[0]&&this.updateAria(e)}}],update:{read:function(){return w(this.mode,"media")&&this.media?{match:this.matchMedia}:!1},write:function(t){var e=t.match,i=this.isToggled(this.target);(e?!i:i)&&this.toggle()},events:["resize"]},methods:{toggle:function(t){var e=this;if(!!m(this.target,t||"toggle",[this])){if(!this.queued)return this.toggleElement(this.target);var i=this.target.filter(function(r){return E(r,e.clsLeave)});if(i.length){this.target.forEach(function(r){var s=w(i,r);e.toggleElement(r,s,s)});return}var n=this.target.filter(this.isToggled);this.toggleElement(n,!1).then(function(){return e.toggleElement(e.target.filter(function(r){return!w(n,r)}),!0)})}},updateAria:function(t){w(this.mode,"media")||M(this.$el,"aria-expanded",pe(t)?t:this.isToggled(this.target))}}},Zo=Object.freeze({__proto__:null,Accordion:Vn,Alert:Fs,Cover:Ls,Drop:Xn,Dropdown:Xn,FormCustom:js,Gif:qs,Grid:Ys,HeightMatch:Js,HeightViewport:Qs,Icon:sr,Img:Eo,Leader:zo,Margin:Kn,Modal:Ho,Nav:Oo,Navbar:Fo,Offcanvas:Lo,OverflowAuto:jo,Responsive:qo,Scroll:Vo,Scrollspy:Yo,ScrollspyNav:Go,Sticky:Xo,Svg:Un,Switcher:mr,Tab:Ko,Toggle:Jo,Video:Yn,Close:yo,Spinner:ko,SlidenavNext:or,SlidenavPrevious:or,SearchIcon:xo,Marker:kt,NavbarToggleIcon:kt,OverlayIcon:kt,PaginationNext:kt,PaginationPrevious:kt,Totop:kt});nt(Zo,function(t,e){return et.component(e,t)}),et.use(zs),Bs(et);var Qo={mixins:[it],props:{date:String,clsWrapper:String},data:{date:"",clsWrapper:".uk-countdown-%unit%"},computed:{date:function(t){var e=t.date;return Date.parse(e)},days:function(t,e){var i=t.clsWrapper;return $(i.replace("%unit%","days"),e)},hours:function(t,e){var i=t.clsWrapper;return $(i.replace("%unit%","hours"),e)},minutes:function(t,e){var i=t.clsWrapper;return $(i.replace("%unit%","minutes"),e)},seconds:function(t,e){var i=t.clsWrapper;return $(i.replace("%unit%","seconds"),e)},units:function(){var t=this;return["days","hours","minutes","seconds"].filter(function(e){return t[e]})}},connected:function(){this.start()},disconnected:function(){var t=this;this.stop(),this.units.forEach(function(e){return Hi(t[e])})},events:[{name:"visibilitychange",el:function(){return document},handler:function(){document.hidden?this.stop():this.start()}}],update:{write:function(){var t=this,e=Uo(this.date);e.total<=0&&(this.stop(),e.days=e.hours=e.minutes=e.seconds=0),this.units.forEach(function(i){var n=String(Math.floor(e[i]));n=n.length<2?"0"+n:n;var r=t[i];r.textContent!==n&&(n=n.split(""),n.length!==r.children.length&&Dt(r,n.map(function(){return"<span></span>"}).join("")),n.forEach(function(s,o){return r.children[o].textContent=s}))})}},methods:{start:function(){this.stop(),this.date&&this.units.length&&(this.$update(),this.timer=setInterval(this.$update,1e3))},stop:function(){this.timer&&(clearInterval(this.timer),this.timer=null)}}};function Uo(t){var e=t-Date.now();return{total:e,seconds:e/1e3%60,minutes:e/1e3/60%60,hours:e/1e3/60/60%24,days:e/1e3/60/60/24}}var en="uk-transition-leave",nn="uk-transition-enter";function wr(t,e,i,n){n===void 0&&(n=0);var r=pi(e,!0),s={opacity:1},o={opacity:0},a=function(f){return function(){return r===pi(e)?f():b.reject()}},u=a(function(){return y(e,en),b.all($r(e).map(function(f,h){return new b(function(l){return setTimeout(function(){return P.start(f,o,i/2,"ease").then(l)},h*n)})})).then(function(){return N(e,en)})}),c=a(function(){var f=q(e);return y(e,nn),t(),d(R(e),{opacity:0}),new b(function(h){return requestAnimationFrame(function(){var l=R(e),v=q(e);d(e,"alignContent","flex-start"),q(e,f);var p=$r(e);d(l,o);var g=p.map(function(x,T){return new b(function(L){return setTimeout(function(){return P.start(x,s,i/2,"ease").then(L)},T*n)})});f!==v&&g.push(P.start(e,{height:v},i/2+p.length*n,"ease")),b.all(g).then(function(){N(e,nn),r===pi(e)&&(d(e,{height:"",alignContent:""}),d(l,{opacity:""}),delete e.dataset.transition),h()})})})});return E(e,en)?br(e).then(c):E(e,nn)?br(e).then(u).then(c):u().then(c)}function pi(t,e){return e&&(t.dataset.transition=1+pi(t)),Nt(t.dataset.transition)||0}function br(t){return b.all(R(t).filter(P.inProgress).map(function(e){return new b(function(i){return X(e,"transitionend transitioncanceled",i)})}))}function $r(t){return Zi(R(t)).reduce(function(e,i){return e.concat(we(i.filter(function(n){return ce(n)}),"offsetLeft"))},[])}function ta(t,e,i){return new b(function(n){return requestAnimationFrame(function(){var r=R(e),s=r.map(function(a){return xr(a,!0)}),o=d(e,["height","padding"]);P.cancel(e),r.forEach(P.cancel),yr(e),t(),r=r.concat(R(e).filter(function(a){return!w(r,a)})),b.resolve().then(function(){F.flush();var a=d(e,["height","padding"]),u=ea(e,r,s),c=u[0],f=u[1];r.forEach(function(h,l){return f[l]&&d(h,f[l])}),d(e,S({display:"block"},o)),requestAnimationFrame(function(){var h=r.map(function(l,v){return z(l)===e&&P.start(l,c[v],i,"ease")}).concat(P.start(e,a,i,"ease"));b.all(h).then(function(){r.forEach(function(l,v){return z(l)===e&&d(l,"display",c[v].opacity===0?"none":"")}),yr(e)},D).then(n)})})})})}function xr(t,e){var i=d(t,"zIndex");return W(t)?S({display:"",opacity:e?d(t,"opacity"):"0",pointerEvents:"none",position:"absolute",zIndex:i==="auto"?oe(t):i},kr(t)):!1}function ea(t,e,i){var n=e.map(function(s,o){return z(s)&&o in i?i[o]?W(s)?kr(s):{opacity:0}:{opacity:W(s)?1:0}:!1}),r=n.map(function(s,o){var a=z(e[o])===t&&(i[o]||xr(e[o]));if(!a)return!1;if(!s)delete a.opacity;else if(!("opacity"in s)){var u=a.opacity;u%1?s.opacity=1:delete a.opacity}return a});return[n,r]}function yr(t){d(t.children,{height:"",left:"",opacity:"",pointerEvents:"",position:"",top:"",marginTop:"",marginLeft:"",transform:"",width:"",zIndex:""}),d(t,{height:"",display:"",padding:""})}function kr(t){var e=B(t),i=e.height,n=e.width,r=ri(t),s=r.top,o=r.left,a=d(t,["marginTop","marginLeft"]),u=a.marginLeft,c=a.marginTop;return{top:s,left:o,height:i,width:n,marginLeft:u,marginTop:c,transform:""}}var Sr={props:{duration:Number,animation:Boolean},data:{duration:150,animation:"slide"},methods:{animate:function(t,e){var i=this;e===void 0&&(e=this.$el);var n=this.animation,r=n==="fade"?wr:n==="delayed-fade"?function(){for(var s=[],o=arguments.length;o--;)s[o]=arguments[o];return wr.apply(void 0,s.concat([40]))}:n?ta:function(){return t(),b.resolve()};return r(t,e,this.duration).then(function(){return i.$update(e,"resize")},D)}}},ia={mixins:[Sr],args:"target",props:{target:Boolean,selActive:Boolean},data:{target:null,selActive:!1,attrItem:"uk-filter-control",cls:"uk-active",duration:250},computed:{toggles:{get:function(t,e){var i=t.attrItem;return O("["+i+"],[data-"+i+"]",e)},watch:function(){var t=this;if(this.updateState(),this.selActive!==!1){var e=O(this.selActive,this.$el);this.toggles.forEach(function(i){return j(i,t.cls,w(e,i))})}},immediate:!0},children:{get:function(t,e){var i=t.target;return O(i+" > *",e)},watch:function(t,e){e&&!oa(t,e)&&this.updateState()},immediate:!0}},events:[{name:"click",delegate:function(){return"["+this.attrItem+"],[data-"+this.attrItem+"]"},handler:function(t){t.preventDefault(),this.apply(t.current)}}],methods:{apply:function(t){var e=this.getState(),i=Cr(t,this.attrItem,this.getState());na(e,i)||this.setState(i)},getState:function(){var t=this;return this.toggles.filter(function(e){return E(e,t.cls)}).reduce(function(e,i){return Cr(i,t.attrItem,e)},{filter:{"":""},sort:[]})},setState:function(t,e){var i=this;e===void 0&&(e=!0),t=S({filter:{"":""},sort:[]},t),m(this.$el,"beforeFilter",[this,t]),this.toggles.forEach(function(n){return j(n,i.cls,!!sa(n,i.attrItem,t))}),b.all(O(this.target,this.$el).map(function(n){var r=function(){ra(t,n,R(n)),i.$update(i.$el)};return e?i.animate(r,n):r()})).then(function(){return m(i.$el,"afterFilter",[i])})},updateState:function(){var t=this;F.write(function(){return t.setState(t.getState(),!1)})}}};function Tr(t,e){return si(ft(t,e),["filter"])}function na(t,e){return["filter","sort"].every(function(i){return Re(t[i],e[i])})}function ra(t,e,i){var n=aa(t);i.forEach(function(u){return d(u,"display",n&&!Y(u,n)?"none":"")});var r=t.sort,s=r[0],o=r[1];if(s){var a=ua(i,s,o);Re(a,i)||K(e,a)}}function Cr(t,e,i){var n=Tr(t,e),r=n.filter,s=n.group,o=n.sort,a=n.order;return a===void 0&&(a="asc"),(r||Z(o))&&(s?r?(delete i.filter[""],i.filter[s]=r):(delete i.filter[s],(Zt(i.filter)||""in i.filter)&&(i.filter={"":r||""})):i.filter={"":r||""}),Z(o)||(i.sort=[o,a]),i}function sa(t,e,i){var n=i.filter;n===void 0&&(n={"":""});var r=i.sort,s=r[0],o=r[1],a=Tr(t,e),u=a.filter;u===void 0&&(u="");var c=a.group;c===void 0&&(c="");var f=a.sort,h=a.order;return h===void 0&&(h="asc"),Z(f)?c in n&&u===n[c]||!u&&c&&!(c in n)&&!n[""]:s===f&&o===h}function oa(t,e){return t.length===e.length&&t.every(function(i){return~e.indexOf(i)})}function aa(t){var e=t.filter,i="";return nt(e,function(n){return i+=n||""}),i}function ua(t,e,i){return S([],t).sort(function(n,r){return ft(n,e).localeCompare(ft(r,e),void 0,{numeric:!0})*(i==="asc"||-1)})}var rn={slide:{show:function(t){return[{transform:V(t*-100)},{transform:V()}]},percent:function(t){return He(t)},translate:function(t,e){return[{transform:V(e*-100*t)},{transform:V(e*100*(1-t))}]}}};function He(t){return Math.abs(d(t,"transform").split(",")[4]/t.offsetWidth)||0}function V(t,e){return t===void 0&&(t=0),e===void 0&&(e="%"),t+=t?e:"",be?"translateX("+t+")":"translate3d("+t+", 0, 0)"}function de(t){return"scale3d("+t+", "+t+", 1)"}var Er=S({},rn,{fade:{show:function(){return[{opacity:0},{opacity:1}]},percent:function(t){return 1-d(t,"opacity")},translate:function(t){return[{opacity:1-t},{opacity:t}]}},scale:{show:function(){return[{opacity:0,transform:de(1-.2)},{opacity:1,transform:de(1)}]},percent:function(t){return 1-d(t,"opacity")},translate:function(t){return[{opacity:1-t,transform:de(1-.2*t)},{opacity:t,transform:de(1-.2+.2*t)}]}}});function ha(t,e,i,n){var r=n.animation,s=n.easing,o=r.percent,a=r.translate,u=r.show;u===void 0&&(u=D);var c=u(i),f=new Xe;return{dir:i,show:function(h,l,v){var p=this;l===void 0&&(l=0);var g=v?"linear":s;return h-=Math.round(h*pt(l,-1,1)),this.translate(l),mi(e,"itemin",{percent:l,duration:h,timing:g,dir:i}),mi(t,"itemout",{percent:1-l,duration:h,timing:g,dir:i}),b.all([P.start(e,c[1],h,g),P.start(t,c[0],h,g)]).then(function(){p.reset(),f.resolve()},D),f.promise},cancel:function(){P.cancel([e,t])},reset:function(){for(var h in c[0])d([e,t],h,"")},forward:function(h,l){return l===void 0&&(l=this.percent()),P.cancel([e,t]),this.show(h,l,!0)},translate:function(h){this.reset();var l=a(h,i);d(e,l[1]),d(t,l[0]),mi(e,"itemtranslatein",{percent:h,dir:i}),mi(t,"itemtranslateout",{percent:1-h,dir:i})},percent:function(){return o(t||e,e,i)},getDistance:function(){return t&&t.offsetWidth}}}function mi(t,e,i){m(t,Ce(e,!1,!1,i))}var ca={props:{autoplay:Boolean,autoplayInterval:Number,pauseOnHover:Boolean},data:{autoplay:!1,autoplayInterval:7e3,pauseOnHover:!0},connected:function(){this.autoplay&&this.startAutoplay()},disconnected:function(){this.stopAutoplay()},update:function(){M(this.slides,"tabindex","-1")},events:[{name:"visibilitychange",el:function(){return document},filter:function(){return this.autoplay},handler:function(){document.hidden?this.stopAutoplay():this.startAutoplay()}}],methods:{startAutoplay:function(){var t=this;this.stopAutoplay(),this.interval=setInterval(function(){return(!t.draggable||!$(":focus",t.$el))&&(!t.pauseOnHover||!Y(t.$el,":hover"))&&!t.stack.length&&t.show("next")},this.autoplayInterval)},stopAutoplay:function(){this.interval&&clearInterval(this.interval)}}},fa={props:{draggable:Boolean},data:{draggable:!0,threshold:10},created:function(){var t=this;["start","move","end"].forEach(function(e){var i=t[e];t[e]=function(n){var r=ae(n).x*(U?-1:1);t.prevPos=r!==t.pos?t.pos:t.prevPos,t.pos=r,i(n)}})},events:[{name:dt,delegate:function(){return this.selSlides},handler:function(t){!this.draggable||!Et(t)&&la(t.target)||ot(t.target,$e)||t.button>0||this.length<2||this.start(t)}},{name:"dragstart",handler:function(t){t.preventDefault()}}],methods:{start:function(){this.drag=this.pos,this._transitioner?(this.percent=this._transitioner.percent(),this.drag+=this._transitioner.getDistance()*this.percent*this.dir,this._transitioner.cancel(),this._transitioner.translate(this.percent),this.dragging=!0,this.stack=[]):this.prevIndex=this.index,A(document,ne,this.move,{passive:!1}),A(document,$t+" "+se+" input",this.end,!0),d(this.list,"userSelect","none")},move:function(t){var e=this,i=this.pos-this.drag;if(!(i===0||this.prevPos===this.pos||!this.dragging&&Math.abs(i)<this.threshold)){d(this.list,"pointerEvents","none"),t.cancelable&&t.preventDefault(),this.dragging=!0,this.dir=i<0?1:-1;for(var n=this,r=n.slides,s=this,o=s.prevIndex,a=Math.abs(i),u=this.getIndex(o+this.dir,o),c=this._getDistance(o,u)||r[o].offsetWidth;u!==o&&a>c;)this.drag-=c*this.dir,o=u,a-=c,u=this.getIndex(o+this.dir,o),c=this._getDistance(o,u)||r[o].offsetWidth;this.percent=a/c;var f=r[o],h=r[u],l=this.index!==u,v=o===u,p;[this.index,this.prevIndex].filter(function(g){return!w([u,o],g)}).forEach(function(g){m(r[g],"itemhidden",[e]),v&&(p=!0,e.prevIndex=o)}),(this.index===o&&this.prevIndex!==o||p)&&m(r[this.index],"itemshown",[this]),l&&(this.prevIndex=o,this.index=u,!v&&m(f,"beforeitemhide",[this]),m(h,"beforeitemshow",[this])),this._transitioner=this._translate(Math.abs(this.percent),f,!v&&h),l&&(!v&&m(f,"itemhide",[this]),m(h,"itemshow",[this]))}},end:function(){if(Wt(document,ne,this.move,{passive:!1}),Wt(document,$t+" "+se+" input",this.end,!0),this.dragging)if(this.dragging=null,this.index===this.prevIndex)this.percent=1-this.percent,this.dir*=-1,this._show(!1,this.index,!0),this._transitioner=null;else{var t=(U?this.dir*(U?1:-1):this.dir)<0==this.prevPos>this.pos;this.index=t?this.index:this.prevIndex,t&&(this.percent=1-this.percent),this.show(this.dir>0&&!t||this.dir<0&&t?"next":"previous",!0)}d(this.list,{userSelect:"",pointerEvents:""}),this.drag=this.percent=null}}};function la(t){return!t.children.length&&t.childNodes.length}var da={data:{selNav:!1},computed:{nav:function(t,e){var i=t.selNav;return $(i,e)},selNavItem:function(t){var e=t.attrItem;return"["+e+"],[data-"+e+"]"},navItems:function(t,e){return O(this.selNavItem,e)}},update:{write:function(){var t=this;this.nav&&this.length!==this.nav.children.length&&Dt(this.nav,this.slides.map(function(e,i){return"<li "+t.attrItem+'="'+i+'"><a href></a></li>'}).join("")),this.navItems.concat(this.nav).forEach(function(e){return e&&(e.hidden=!t.maxIndex)}),this.updateNav()},events:["resize"]},events:[{name:"click",delegate:function(){return this.selNavItem},handler:function(t){t.preventDefault(),this.show(ft(t.current,this.attrItem))}},{name:"itemshow",handler:"updateNav"}],methods:{updateNav:function(){var t=this,e=this.getValidIndex();this.navItems.forEach(function(i){var n=ft(i,t.attrItem);j(i,t.clsActive,Nt(n)===e),j(i,"uk-invisible",t.finite&&(n==="previous"&&e===0||n==="next"&&e>=t.maxIndex))})}}},Ir={mixins:[ca,fa,da],props:{clsActivated:Boolean,easing:String,index:Number,finite:Boolean,velocity:Number,selSlides:String},data:function(){return{easing:"ease",finite:!1,velocity:1,index:0,prevIndex:-1,stack:[],percent:0,clsActive:"uk-active",clsActivated:!1,Transitioner:!1,transitionOptions:{}}},connected:function(){this.prevIndex=-1,this.index=this.getValidIndex(this.$props.index),this.stack=[]},disconnected:function(){N(this.slides,this.clsActive)},computed:{duration:function(t,e){var i=t.velocity;return _r(e.offsetWidth/i)},list:function(t,e){var i=t.selList;return $(i,e)},maxIndex:function(){return this.length-1},selSlides:function(t){var e=t.selList,i=t.selSlides;return e+" "+(i||"> *")},slides:{get:function(){return O(this.selSlides,this.$el)},watch:function(){this.$reset()}},length:function(){return this.slides.length}},events:{itemshown:function(){this.$update(this.list)}},methods:{show:function(t,e){var i=this;if(e===void 0&&(e=!1),!(this.dragging||!this.length)){var n=this,r=n.stack,s=e?0:r.length,o=function(){r.splice(s,1),r.length&&i.show(r.shift(),!0)};if(r[e?"unshift":"push"](t),!e&&r.length>1){r.length===2&&this._transitioner.forward(Math.min(this.duration,200));return}var a=this.getIndex(this.index),u=E(this.slides,this.clsActive)&&this.slides[a],c=this.getIndex(t,this.index),f=this.slides[c];if(u===f){o();return}if(this.dir=va(t,a),this.prevIndex=a,this.index=c,u&&!m(u,"beforeitemhide",[this])||!m(f,"beforeitemshow",[this,u])){this.index=this.prevIndex,o();return}var h=this._show(u,f,e).then(function(){return u&&m(u,"itemhidden",[i]),m(f,"itemshown",[i]),new b(function(l){F.write(function(){r.shift(),r.length?i.show(r.shift(),!0):i._transitioner=null,l()})})});return u&&m(u,"itemhide",[this]),m(f,"itemshow",[this]),h}},getIndex:function(t,e){return t===void 0&&(t=this.index),e===void 0&&(e=this.index),pt(Ut(t,this.slides,e,this.finite),0,this.maxIndex)},getValidIndex:function(t,e){return t===void 0&&(t=this.index),e===void 0&&(e=this.prevIndex),this.getIndex(t,e)},_show:function(t,e,i){if(this._transitioner=this._getTransitioner(t,e,this.dir,S({easing:i?e.offsetWidth<600?"cubic-bezier(0.25, 0.46, 0.45, 0.94)":"cubic-bezier(0.165, 0.84, 0.44, 1)":this.easing},this.transitionOptions)),!i&&!t)return this._translate(1),b.resolve();var n=this.stack,r=n.length;return this._transitioner[r>1?"forward":"show"](r>1?Math.min(this.duration,75+75/(r-1)):this.duration,this.percent)},_getDistance:function(t,e){return this._getTransitioner(t,t!==e&&e).getDistance()},_translate:function(t,e,i){e===void 0&&(e=this.prevIndex),i===void 0&&(i=this.index);var n=this._getTransitioner(e!==i?e:!1,i);return n.translate(t),n},_getTransitioner:function(t,e,i,n){return t===void 0&&(t=this.prevIndex),e===void 0&&(e=this.index),i===void 0&&(i=this.dir||1),n===void 0&&(n=this.transitionOptions),new this.Transitioner(Jt(t)?this.slides[t]:t,Jt(e)?this.slides[e]:e,i*(U?-1:1),n)}}};function va(t,e){return t==="next"?1:t==="previous"||t<e?-1:1}function _r(t){return .5*t+300}var Ar={mixins:[Ir],props:{animation:String},data:{animation:"slide",clsActivated:"uk-transition-active",Animations:rn,Transitioner:ha},computed:{animation:function(t){var e=t.animation,i=t.Animations;return S(i[e]||i.slide,{name:e})},transitionOptions:function(){return{animation:this.animation}}},events:{"itemshow itemhide itemshown itemhidden":function(t){var e=t.target;this.$update(e)},beforeitemshow:function(t){var e=t.target;y(e,this.clsActive)},itemshown:function(t){var e=t.target;y(e,this.clsActivated)},itemhidden:function(t){var e=t.target;N(e,this.clsActive,this.clsActivated)}}},Pr={mixins:[le,tn,_t,Ar],functional:!0,props:{delayControls:Number,preload:Number,videoAutoplay:Boolean,template:String},data:function(){return{preload:1,videoAutoplay:!1,delayControls:3e3,items:[],cls:"uk-open",clsPage:"uk-lightbox-page",selList:".uk-lightbox-items",attrItem:"uk-lightbox-item",selClose:".uk-close-large",selCaption:".uk-lightbox-caption",pauseOnHover:!1,velocity:2,Animations:Er,template:'<div class="uk-lightbox uk-overflow-hidden"> <ul class="uk-lightbox-items"></ul> <div class="uk-lightbox-toolbar uk-position-top uk-text-right uk-transition-slide-top uk-transition-opaque"> <button class="uk-lightbox-toolbar-icon uk-close-large" type="button" uk-close></button> </div> <a class="uk-lightbox-button uk-position-center-left uk-position-medium uk-transition-fade" href uk-slidenav-previous uk-lightbox-item="previous"></a> <a class="uk-lightbox-button uk-position-center-right uk-position-medium uk-transition-fade" href uk-slidenav-next uk-lightbox-item="next"></a> <div class="uk-lightbox-toolbar uk-lightbox-caption uk-position-bottom uk-text-center uk-transition-slide-bottom uk-transition-opaque"></div> </div>'}},created:function(){var t=$(this.template),e=$(this.selList,t);this.items.forEach(function(){return K(e,"<li>")}),this.$mount(K(this.container,t))},computed:{caption:function(t,e){var i=t.selCaption;return $(i,e)}},events:[{name:ne+" "+dt+" keydown",handler:"showControls"},{name:"click",self:!0,delegate:function(){return this.selSlides},handler:function(t){t.defaultPrevented||this.hide()}},{name:"shown",self:!0,handler:function(){this.showControls()}},{name:"hide",self:!0,handler:function(){this.hideControls(),N(this.slides,this.clsActive),P.stop(this.slides)}},{name:"hidden",self:!0,handler:function(){this.$destroy(!0)}},{name:"keyup",el:function(){return document},handler:function(t){if(!(!this.isToggled(this.$el)||!this.draggable))switch(t.keyCode){case 37:this.show("previous");break;case 39:this.show("next");break}}},{name:"beforeitemshow",handler:function(t){this.isToggled()||(this.draggable=!1,t.preventDefault(),this.toggleElement(this.$el,!0,!1),this.animation=Er.scale,N(t.target,this.clsActive),this.stack.splice(1,0,this.index))}},{name:"itemshow",handler:function(){Dt(this.caption,this.getItem().caption||"");for(var t=-this.preload;t<=this.preload;t++)this.loadItem(this.index+t)}},{name:"itemshown",handler:function(){this.draggable=this.$props.draggable}},{name:"itemload",handler:function(t,e){var i=this,n=e.source,r=e.type,s=e.alt;s===void 0&&(s="");var o=e.poster,a=e.attrs;if(a===void 0&&(a={}),this.setItem(e,"<span uk-spinner></span>"),!!n){var u,c={frameborder:"0",allow:"autoplay",allowfullscreen:"",style:"max-width: 100%; box-sizing: border-box;","uk-responsive":"","uk-video":""+this.videoAutoplay};if(r==="image"||n.match(/\.(avif|jpe?g|a?png|gif|svg|webp)($|\?)/i))zi(n,a.srcset,a.size).then(function(h){var l=h.width,v=h.height;return i.setItem(e,Be("img",S({src:n,width:l,height:v,alt:s},a)))},function(){return i.setError(e)});else if(r==="video"||n.match(/\.(mp4|webm|ogv)($|\?)/i)){var f=Be("video",S({src:n,poster:o,controls:"",playsinline:"","uk-video":""+this.videoAutoplay},a));A(f,"loadedmetadata",function(){M(f,{width:f.videoWidth,height:f.videoHeight}),i.setItem(e,f)}),A(f,"error",function(){return i.setError(e)})}else r==="iframe"||n.match(/\.(html|php)($|\?)/i)?this.setItem(e,Be("iframe",S({src:n,frameborder:"0",allowfullscreen:"",class:"uk-lightbox-iframe"},a))):(u=n.match(/\/\/(?:.*?youtube(-nocookie)?\..*?[?&]v=|youtu\.be\/)([\w-]{11})[&?]?(.*)?/))?this.setItem(e,Be("iframe",S({src:"https://www.youtube"+(u[1]||"")+".com/embed/"+u[2]+(u[3]?"?"+u[3]:""),width:1920,height:1080},c,a))):(u=n.match(/\/\/.*?vimeo\.[a-z]+\/(\d+)[&?]?(.*)?/))&&Je("https://vimeo.com/api/oembed.json?maxwidth=1920&url="+encodeURI(n),{responseType:"json",withCredentials:!1}).then(function(h){var l=h.response,v=l.height,p=l.width;return i.setItem(e,Be("iframe",S({src:"https://player.vimeo.com/video/"+u[1]+(u[2]?"?"+u[2]:""),width:p,height:v},c,a)))},function(){return i.setError(e)})}}}],methods:{loadItem:function(t){t===void 0&&(t=this.index);var e=this.getItem(t);this.getSlide(e).childElementCount||m(this.$el,"itemload",[e])},getItem:function(t){return t===void 0&&(t=this.index),this.items[Ut(t,this.slides)]},setItem:function(t,e){m(this.$el,"itemloaded",[this,Dt(this.getSlide(t),e)])},getSlide:function(t){return this.slides[this.items.indexOf(t)]},setError:function(t){this.setItem(t,'<span uk-icon="icon: bolt; ratio: 2"></span>')},showControls:function(){clearTimeout(this.controlsTimer),this.controlsTimer=setTimeout(this.hideControls,this.delayControls),y(this.$el,"uk-active","uk-transition-active")},hideControls:function(){N(this.$el,"uk-active","uk-transition-active")}}};function Be(t,e){var i=ue("<"+t+">");return M(i,e),i}var ga={install:pa,props:{toggle:String},data:{toggle:"a"},computed:{toggles:{get:function(t,e){var i=t.toggle;return O(i,e)},watch:function(){this.hide()}}},disconnected:function(){this.hide()},events:[{name:"click",delegate:function(){return this.toggle+":not(.uk-disabled)"},handler:function(t){t.preventDefault(),this.show(t.current)}}],methods:{show:function(t){var e=this,i=fn(this.toggles.map(Mr),"source");if(Ot(t)){var n=Mr(t),r=n.source;t=Pt(i,function(s){var o=s.source;return r===o})}return this.panel=this.panel||this.$create("lightboxPanel",S({},this.$props,{items:i})),A(this.panel.$el,"hidden",function(){return e.panel=!1}),this.panel.show(t)},hide:function(){return this.panel&&this.panel.hide()}}};function pa(t,e){t.lightboxPanel||t.component("lightboxPanel",Pr),S(e.props,t.component("lightboxPanel").options.props)}function Mr(t){var e={};return["href","caption","type","poster","alt","attrs"].forEach(function(i){e[i==="href"?"source":i]=ft(t,i)}),e.attrs=si(e.attrs),e}var wi,ma={mixins:[le],functional:!0,args:["message","status"],data:{message:"",status:"",timeout:5e3,group:null,pos:"top-center",clsContainer:"uk-notification",clsClose:"uk-notification-close",clsMsg:"uk-notification-message"},install:wa,computed:{marginProp:function(t){var e=t.pos;return"margin"+(vt(e,"top")?"Top":"Bottom")},startProps:function(){var t;return t={opacity:0},t[this.marginProp]=-this.$el.offsetHeight,t}},created:function(){var t=$("."+this.clsContainer+"-"+this.pos,this.container)||K(this.container,'<div class="'+this.clsContainer+" "+this.clsContainer+"-"+this.pos+'" style="display: block"></div>');this.$mount(K(t,'<div class="'+this.clsMsg+(this.status?" "+this.clsMsg+"-"+this.status:"")+'"> <a href class="'+this.clsClose+'" data-uk-close></a> <div>'+this.message+"</div> </div>"))},connected:function(){var t=this,e,i=k(d(this.$el,this.marginProp));P.start(d(this.$el,this.startProps),(e={opacity:1},e[this.marginProp]=i,e)).then(function(){t.timeout&&(t.timer=setTimeout(t.close,t.timeout))})},events:(wi={click:function(t){ot(t.target,'a[href="#"],a[href=""]')&&t.preventDefault(),this.close()}},wi[Lt]=function(){this.timer&&clearTimeout(this.timer)},wi[re]=function(){this.timeout&&(this.timer=setTimeout(this.close,this.timeout))},wi),methods:{close:function(t){var e=this,i=function(n){var r=z(n);m(n,"close",[e]),rt(n),r&&!r.hasChildNodes()&&rt(r)};this.timer&&clearTimeout(this.timer),t?i(this.$el):P.start(this.$el,this.startProps).then(i)}}};function wa(t){t.notification.closeAll=function(e,i){It(document.body,function(n){var r=t.getComponent(n,"notification");r&&(!e||e===r.group)&&r.close(i)})}}var sn=["x","y","bgx","bgy","rotate","scale","color","backgroundColor","borderColor","opacity","blur","hue","grayscale","invert","saturate","sepia","fopacity","stroke"],Nr={mixins:[gi],props:sn.reduce(function(t,e){return t[e]="list",t},{}),data:sn.reduce(function(t,e){return t[e]=void 0,t},{}),computed:{props:function(t,e){var i=this;return sn.reduce(function(n,r){if(Z(t[r]))return n;var s=r.match(/color/i),o=s||r==="opacity",a,u,c,f=t[r].slice();o&&d(e,r,""),f.length<2&&f.unshift((r==="scale"?1:o?d(e,r):0)||0);var h=xa(f);if(s){var l=e.style,v=l.color;f=f.map(function(_){return ba(e,_)}),e.style.color=v}else if(vt(r,"bg")){var p=r==="bgy"?"height":"width";if(f=f.map(function(_){return yt(_,p,i.$el)}),d(e,"background-position-"+r[2],""),u=d(e,"backgroundPosition").split(" ")[r[2]==="x"?0:1],i.covers){var g=Math.min.apply(Math,f),x=Math.max.apply(Math,f),T=f.indexOf(g)<f.indexOf(x);c=x-g,f=f.map(function(_){return _-(T?g:x)}),a=(T?-c:0)+"px"}else a=u}else f=f.map(k);if(r==="stroke"){if(!f.some(function(_){return _}))return n;var L=er(i.$el);d(e,"strokeDasharray",L),h==="%"&&(f=f.map(function(_){return _*L/100})),f=f.reverse(),r="strokeDashoffset"}return n[r]={steps:f,unit:h,pos:a,bgPos:u,diff:c},n},{})},bgProps:function(){var t=this;return["bgx","bgy"].filter(function(e){return e in t.props})},covers:function(t,e){return ya(e)}},disconnected:function(){delete this._image},update:{read:function(t){var e=this;if(!!this.matchMedia){if(!t.image&&this.covers&&this.bgProps.length){var i=d(this.$el,"backgroundImage").replace(/^none|url\(["']?(.+?)["']?\)$/,"$1");if(i){var n=new Image;n.src=i,t.image=n,n.naturalWidth||(n.onload=function(){return e.$update()})}}var r=t.image;if(!(!r||!r.naturalWidth)){var s={width:this.$el.offsetWidth,height:this.$el.offsetHeight},o={width:r.naturalWidth,height:r.naturalHeight},a=Qt.cover(o,s);this.bgProps.forEach(function(u){var c=e.props[u],f=c.diff,h=c.bgPos,l=c.steps,v=u==="bgy"?"height":"width",p=a[v]-s[v];if(p<f)s[v]=a[v]+f-p;else if(p>f){var g=s[v]/yt(h,v,e.$el);g&&(e.props[u].steps=l.map(function(x){return x-(p-f)/g}))}a=Qt.cover(o,s)}),t.dim=a}}},write:function(t){var e=t.dim;if(!this.matchMedia){d(this.$el,{backgroundSize:"",backgroundRepeat:""});return}e&&d(this.$el,{backgroundSize:e.width+"px "+e.height+"px",backgroundRepeat:"no-repeat"})},events:["resize"]},methods:{reset:function(){var t=this;nt(this.getCss(0),function(e,i){return d(t.$el,i,"")})},getCss:function(t){var e=this,i=e.props;return Object.keys(i).reduce(function(n,r){var s=i[r],o=s.steps,a=s.unit,u=s.pos,c=$a(o,t);switch(r){case"x":case"y":{a=a||"px",n.transform+=" translate"+We(r)+"("+k(c).toFixed(a==="px"?0:2)+a+")";break}case"rotate":a=a||"deg",n.transform+=" rotate("+(c+a)+")";break;case"scale":n.transform+=" scale("+c+")";break;case"bgy":case"bgx":n["background-position-"+r[2]]="calc("+u+" + "+c+"px)";break;case"color":case"backgroundColor":case"borderColor":{var f=Dr(o,t),h=f[0],l=f[1],v=f[2];n[r]="rgba("+h.map(function(p,g){return p=p+v*(l[g]-p),g===3?k(p):parseInt(p,10)}).join(",")+")";break}case"blur":a=a||"px",n.filter+=" blur("+(c+a)+")";break;case"hue":a=a||"deg",n.filter+=" hue-rotate("+(c+a)+")";break;case"fopacity":a=a||"%",n.filter+=" opacity("+(c+a)+")";break;case"grayscale":case"invert":case"saturate":case"sepia":a=a||"%",n.filter+=" "+r+"("+(c+a)+")";break;default:n[r]=c}return n},{transform:"",filter:""})}}};function ba(t,e){return d(d(t,"color",e),"color").split(/[(),]/g).slice(1,-1).concat(1).slice(0,4).map(k)}function Dr(t,e){var i=t.length-1,n=Math.min(Math.floor(i*e),i-1),r=t.slice(n,n+2);return r.push(e===1?1:e%(1/i)*i),r}function $a(t,e,i){i===void 0&&(i=2);var n=Dr(t,e),r=n[0],s=n[1],o=n[2];return(Jt(r)?r+Math.abs(r-s)*o*(r<s?1:-1):+s).toFixed(i)}function xa(t){return t.reduce(function(e,i){return H(i)&&i.replace(/-|\d/g,"").trim()||e},"")}function ya(t){var e=t.style,i=e.backgroundSize,n=d(d(t,"backgroundSize",""),"backgroundSize")==="cover";return t.style.backgroundSize=i,n}var ka={mixins:[Nr],props:{target:String,viewport:Number,easing:Number},data:{target:!1,viewport:1,easing:1},computed:{target:function(t,e){var i=t.target;return zr(i&&wt(i,e)||e)}},update:{read:function(t,e){var i=t.percent;if(e.has("scroll")||(i=!1),!!this.matchMedia){var n=i;return i=Sa(Ji(this.target)/(this.viewport||1),this.easing),{percent:i,style:n!==i?this.getCss(i):!1}}},write:function(t){var e=t.style;if(!this.matchMedia){this.reset();return}e&&d(this.$el,e)},events:["scroll","resize"]}};function Sa(t,e){return pt(t*(1-(e-e*t)))}function zr(t){return t?"offsetTop"in t?t:zr(z(t)):document.body}var Hr={update:{write:function(){if(!(this.stack.length||this.dragging)){var t=this.getValidIndex(this.index);(!~this.prevIndex||this.index!==t)&&this.show(t)}},events:["resize"]}};function Ta(t,e,i,n){var r=n.center,s=n.easing,o=n.list,a=new Xe,u=t?Oe(t,o,r):Oe(e,o,r)+I(e).width*i,c=e?Oe(e,o,r):u+I(t).width*i*(U?-1:1);return{dir:i,show:function(f,h,l){h===void 0&&(h=0);var v=l?"linear":s;return f-=Math.round(f*pt(h,-1,1)),this.translate(h),h=t?h:pt(h,0,1),on(this.getItemIn(),"itemin",{percent:h,duration:f,timing:v,dir:i}),t&&on(this.getItemIn(!0),"itemout",{percent:1-h,duration:f,timing:v,dir:i}),P.start(o,{transform:V(-c*(U?-1:1),"px")},f,v).then(a.resolve,D),a.promise},cancel:function(){P.cancel(o)},reset:function(){d(o,"transform","")},forward:function(f,h){return h===void 0&&(h=this.percent()),P.cancel(o),this.show(f,h,!0)},translate:function(f){var h=this.getDistance()*i*(U?-1:1);d(o,"transform",V(pt(-c+(h-h*f),-bi(o),I(o).width)*(U?-1:1),"px"));var l=this.getActives(),v=this.getItemIn(),p=this.getItemIn(!0);f=t?pt(f,-1,1):0,R(o).forEach(function(g){var x=w(l,g),T=g===v,L=g===p,_=T||!L&&(x||i*(U?-1:1)==-1^$i(g,o)>$i(t||e));on(g,"itemtranslate"+(_?"in":"out"),{dir:i,percent:L?1-f:T?f:x?1:0})})},percent:function(){return Math.abs((d(o,"transform").split(",")[4]*(U?-1:1)+u)/(c-u))},getDistance:function(){return Math.abs(c-u)},getItemIn:function(f){f===void 0&&(f=!1);var h=this.getActives(),l=Or(o,Oe(e||t,o,r));if(f){var v=h;h=l,l=v}return l[Pt(l,function(p){return!w(h,p)})]},getActives:function(){return Or(o,Oe(t||e,o,r))}}}function Oe(t,e,i){var n=$i(t,e);return i?n-Ca(t,e):Math.min(n,Br(e))}function Br(t){return Math.max(0,bi(t)-I(t).width)}function bi(t){return R(t).reduce(function(e,i){return I(i).width+e},0)}function Ca(t,e){return I(e).width/2-I(t).width/2}function $i(t,e){return t&&(ri(t).left+(U?I(t).width-I(e).width:0))*(U?-1:1)||0}function Or(t,e){e-=1;var i=I(t).width,n=e+i+2;return R(t).filter(function(r){var s=$i(r,t),o=s+Math.min(I(r).width,i);return s>=e&&o<=n})}function on(t,e,i){m(t,Ce(e,!1,!1,i))}var Ea={mixins:[it,Ir,Hr],props:{center:Boolean,sets:Boolean},data:{center:!1,sets:!1,attrItem:"uk-slider-item",selList:".uk-slider-items",selNav:".uk-slider-nav",clsContainer:"uk-slider-container",Transitioner:Ta},computed:{avgWidth:function(){return bi(this.list)/this.length},finite:function(t){var e=t.finite;return e||Math.ceil(bi(this.list))<I(this.list).width+Ia(this.list)+this.center},maxIndex:function(){if(!this.finite||this.center&&!this.sets)return this.length-1;if(this.center)return me(this.sets);var t=0,e=Br(this.list),i=Pt(this.slides,function(n){if(t>=e)return!0;t+=I(n).width});return~i?i:this.length-1},sets:function(t){var e=this,i=t.sets;if(!!i){var n=I(this.list).width/(this.center?2:1),r=0,s=n,o=0;return i=we(this.slides,"offsetLeft").reduce(function(a,u,c){var f=I(u).width,h=o+f;if(h>r&&(!e.center&&c>e.maxIndex&&(c=e.maxIndex),!w(a,c))){var l=e.slides[c+1];e.center&&l&&f<s-I(l).width/2?s-=f:(s=n,a.push(c),r=o+n+(e.center?f/2:0))}return o+=f,a},[]),!Zt(i)&&i}},transitionOptions:function(){return{center:this.center,list:this.list}}},connected:function(){j(this.$el,this.clsContainer,!$("."+this.clsContainer,this.$el))},update:{write:function(){var t=this;this.navItems.forEach(function(i){var n=Nt(ft(i,t.attrItem));n!==!1&&(i.hidden=!t.maxIndex||n>t.maxIndex||t.sets&&!w(t.sets,n))}),this.length&&!this.dragging&&!this.stack.length&&(this.reorder(),this._translate(1));var e=this._getTransitioner(this.index).getActives();this.slides.forEach(function(i){return j(i,t.clsActive,w(e,i))}),this.clsActivated&&(!this.sets||w(this.sets,k(this.index)))&&this.slides.forEach(function(i){return j(i,t.clsActivated||"",w(e,i))})},events:["resize"]},events:{beforeitemshow:function(t){!this.dragging&&this.sets&&this.stack.length<2&&!w(this.sets,this.index)&&(this.index=this.getValidIndex());var e=Math.abs(this.index-this.prevIndex+(this.dir>0&&this.index<this.prevIndex||this.dir<0&&this.index>this.prevIndex?(this.maxIndex+1)*this.dir:0));if(!this.dragging&&e>1){for(var i=0;i<e;i++)this.stack.splice(1,0,this.dir>0?"next":"previous");t.preventDefault();return}var n=this.dir<0||!this.slides[this.prevIndex]?this.index:this.prevIndex;this.duration=_r(this.avgWidth/this.velocity)*(I(this.slides[n]).width/this.avgWidth),this.reorder()},itemshow:function(){~this.prevIndex&&y(this._getTransitioner().getItemIn(),this.clsActive)}},methods:{reorder:function(){var t=this;if(this.finite){d(this.slides,"order","");return}var e=this.dir>0&&this.slides[this.prevIndex]?this.prevIndex:this.index;if(this.slides.forEach(function(a,u){return d(a,"order",t.dir>0&&u<e?1:t.dir<0&&u>=t.index?-1:"")}),!!this.center)for(var i=this.slides[e],n=I(this.list).width/2-I(i).width/2,r=0;n>0;){var s=this.getIndex(--r+e,e),o=this.slides[s];d(o,"order",s>e?-2:-1),n-=I(o).width}},getValidIndex:function(t,e){if(t===void 0&&(t=this.index),e===void 0&&(e=this.prevIndex),t=this.getIndex(t,e),!this.sets)return t;var i;do{if(w(this.sets,t))return t;i=t,t=this.getIndex(t+this.dir,e)}while(t!==i);return t}}};function Ia(t){return Math.max.apply(Math,[0].concat(R(t).map(function(e){return I(e).width})))}var Fr={mixins:[Nr],data:{selItem:"!li"},computed:{item:function(t,e){var i=t.selItem;return wt(i,e)}},events:[{name:"itemin itemout",self:!0,el:function(){return this.item},handler:function(t){var e=this,i=t.type,n=t.detail,r=n.percent,s=n.duration,o=n.timing,a=n.dir;F.read(function(){var u=e.getCss(Wr(i,a,r)),c=e.getCss(Lr(i)?.5:a>0?1:0);F.write(function(){d(e.$el,u),P.start(e.$el,c,s,o).catch(D)})})}},{name:"transitioncanceled transitionend",self:!0,el:function(){return this.item},handler:function(){P.cancel(this.$el)}},{name:"itemtranslatein itemtranslateout",self:!0,el:function(){return this.item},handler:function(t){var e=this,i=t.type,n=t.detail,r=n.percent,s=n.dir;F.read(function(){var o=e.getCss(Wr(i,s,r));F.write(function(){return d(e.$el,o)})})}}]};function Lr(t){return gt(t,"in")}function Wr(t,e,i){return i/=2,Lr(t)?e<0?1-i:i:e<0?i:1-i}var _a=S({},rn,{fade:{show:function(){return[{opacity:0,zIndex:0},{zIndex:-1}]},percent:function(t){return 1-d(t,"opacity")},translate:function(t){return[{opacity:1-t,zIndex:0},{zIndex:-1}]}},scale:{show:function(){return[{opacity:0,transform:de(1+.5),zIndex:0},{zIndex:-1}]},percent:function(t){return 1-d(t,"opacity")},translate:function(t){return[{opacity:1-t,transform:de(1+.5*t),zIndex:0},{zIndex:-1}]}},pull:{show:function(t){return t<0?[{transform:V(30),zIndex:-1},{transform:V(),zIndex:0}]:[{transform:V(-100),zIndex:0},{transform:V(),zIndex:-1}]},percent:function(t,e,i){return i<0?1-He(e):He(t)},translate:function(t,e){return e<0?[{transform:V(30*t),zIndex:-1},{transform:V(-100*(1-t)),zIndex:0}]:[{transform:V(-t*100),zIndex:0},{transform:V(30*(1-t)),zIndex:-1}]}},push:{show:function(t){return t<0?[{transform:V(100),zIndex:0},{transform:V(),zIndex:-1}]:[{transform:V(-30),zIndex:-1},{transform:V(),zIndex:0}]},percent:function(t,e,i){return i>0?1-He(e):He(t)},translate:function(t,e){return e<0?[{transform:V(t*100),zIndex:0},{transform:V(-30*(1-t)),zIndex:-1}]:[{transform:V(-30*t),zIndex:-1},{transform:V(100*(1-t)),zIndex:0}]}}}),Aa={mixins:[it,Ar,Hr],props:{ratio:String,minHeight:Number,maxHeight:Number},data:{ratio:"16:9",minHeight:!1,maxHeight:!1,selList:".uk-slideshow-items",attrItem:"uk-slideshow-item",selNav:".uk-slideshow-nav",Animations:_a},update:{read:function(){var t=this.ratio.split(":").map(Number),e=t[0],i=t[1];return i=i*this.list.offsetWidth/e||0,this.minHeight&&(i=Math.max(this.minHeight,i)),this.maxHeight&&(i=Math.min(this.maxHeight,i)),{height:i-Rt(this.list,"height","content-box")}},write:function(t){var e=t.height;e>0&&d(this.list,"minHeight",e)},events:["resize"]}},Pa={mixins:[it,Sr],props:{group:String,threshold:Number,clsItem:String,clsPlaceholder:String,clsDrag:String,clsDragState:String,clsBase:String,clsNoDrag:String,clsEmpty:String,clsCustom:String,handle:String},data:{group:!1,threshold:5,clsItem:"uk-sortable-item",clsPlaceholder:"uk-sortable-placeholder",clsDrag:"uk-sortable-drag",clsDragState:"uk-drag",clsBase:"uk-sortable",clsNoDrag:"uk-sortable-nodrag",clsEmpty:"uk-sortable-empty",clsCustom:"",handle:!1,pos:{}},created:function(){var t=this;["init","start","move","end"].forEach(function(e){var i=t[e];t[e]=function(n){S(t.pos,ae(n)),i(n)}})},events:{name:dt,passive:!1,handler:"init"},computed:{target:function(){return(this.$el.tBodies||[this.$el])[0]},items:function(){return R(this.target)},isEmpty:{get:function(){return Zt(this.items)},watch:function(t){j(this.target,this.clsEmpty,t)},immediate:!0},handles:{get:function(t,e){var i=t.handle;return i?O(i,e):this.items},watch:function(t,e){d(e,{touchAction:"",userSelect:""}),d(t,{touchAction:dn?"none":"",userSelect:"none"})},immediate:!0}},update:{write:function(t){if(!(!this.drag||!z(this.placeholder))){var e=this,i=e.pos,n=i.x,r=i.y,s=e.origin,o=s.offsetTop,a=s.offsetLeft,u=e.placeholder;d(this.drag,{top:r-o,left:n-a});var c=this.getSortable(document.elementFromPoint(n,r));if(!!c){var f=c.items;if(!f.some(P.inProgress)){var h=za(f,{x:n,y:r});if(!(f.length&&(!h||h===u))){var l=this.getSortable(u),v=Ha(c.target,h,u,n,r,c===l&&t.moved!==h);v!==!1&&(v&&u===v||(c!==l?(l.remove(u),t.moved=h):delete t.moved,c.insert(u,v),this.touched.add(c)))}}}}},events:["move"]},methods:{init:function(t){var e=t.target,i=t.button,n=t.defaultPrevented,r=this.items.filter(function(o){return G(e,o)}),s=r[0];!s||n||i>0||Ni(e)||G(e,"."+this.clsNoDrag)||this.handle&&!G(e,this.handle)||(t.preventDefault(),this.touched=new Set([this]),this.placeholder=s,this.origin=S({target:e,index:oe(s)},this.pos),A(document,ne,this.move),A(document,$t,this.end),this.threshold||this.start(t))},start:function(t){this.drag=Da(this.$container,this.placeholder);var e=this.placeholder.getBoundingClientRect(),i=e.left,n=e.top;S(this.origin,{offsetLeft:this.pos.x-i,offsetTop:this.pos.y-n}),y(this.drag,this.clsDrag,this.clsCustom),y(this.placeholder,this.clsPlaceholder),y(this.items,this.clsItem),y(document.documentElement,this.clsDragState),m(this.$el,"start",[this,this.placeholder]),Ma(this.pos),this.move(t)},move:function(t){this.drag?this.$emit("move"):(Math.abs(this.pos.x-this.origin.x)>this.threshold||Math.abs(this.pos.y-this.origin.y)>this.threshold)&&this.start(t)},end:function(){var t=this;if(Wt(document,ne,this.move),Wt(document,$t,this.end),Wt(window,"scroll",this.scroll),!!this.drag){Na();var e=this.getSortable(this.placeholder);this===e?this.origin.index!==oe(this.placeholder)&&m(this.$el,"moved",[this,this.placeholder]):(m(e.$el,"added",[e,this.placeholder]),m(this.$el,"removed",[this,this.placeholder])),m(this.$el,"stop",[this,this.placeholder]),rt(this.drag),this.drag=null,this.touched.forEach(function(i){var n=i.clsPlaceholder,r=i.clsItem;return t.touched.forEach(function(s){return N(s.items,n,r)})}),this.touched=null,N(document.documentElement,this.clsDragState)}},insert:function(t,e){var i=this;y(this.items,this.clsItem);var n=function(){return e?Ie(e,t):K(i.target,t)};this.animate(n)},remove:function(t){!G(t,this.target)||this.animate(function(){return rt(t)})},getSortable:function(t){do{var e=this.$getComponent(t,"sortable");if(e&&(e===this||this.group!==!1&&e.group===this.group))return e}while(t=z(t))}}},Rr;function Ma(t){var e=Date.now();Rr=setInterval(function(){var i=t.x,n=t.y;n+=window.pageYOffset;var r=(Date.now()-e)*.3;e=Date.now(),jt(document.elementFromPoint(i,t.y)).reverse().some(function(s){var o=s.scrollTop,a=s.scrollHeight,u=B(fe(s)),c=u.top,f=u.bottom,h=u.height;if(c<n&&c+35>n)o-=r;else if(f>n&&f-35<n)o+=r;else return;if(o>0&&o<a-h)return ui(s,o),!0})},15)}function Na(){clearInterval(Rr)}function Da(t,e){var i=K(t,e.outerHTML.replace(/(^<)(?:li|tr)|(?:li|tr)(\/>$)/g,"$1div$2"));return d(i,"margin","0","important"),d(i,S({boxSizing:"border-box",width:e.offsetWidth,height:e.offsetHeight},d(e,["paddingLeft","paddingRight","paddingTop","paddingBottom"]))),q(i.firstElementChild,q(e.firstElementChild)),i}function za(t,e){return t[Pt(t,function(i){return je(e,i.getBoundingClientRect())})]}function Ha(t,e,i,n,r,s){if(!!R(t).length){var o=e.getBoundingClientRect();if(!s)return Ba(t,i)||r<o.top+o.height/2?e:e.nextElementSibling;var a=i.getBoundingClientRect(),u=jr([o.top,o.bottom],[a.top,a.bottom]),c=u?n:r,f=u?"width":"height",h=u?"left":"top",l=u?"right":"bottom",v=a[f]<o[f]?o[f]-a[f]:0;return a[h]<o[h]?v&&c<o[h]+v?!1:e.nextElementSibling:v&&c>o[l]-v?!1:e}}function Ba(t,e){var i=R(t).length===1;i&&K(t,e);var n=R(t),r=n.some(function(s,o){var a=s.getBoundingClientRect();return n.slice(o+1).some(function(u){var c=u.getBoundingClientRect();return!jr([a.left,a.right],[c.left,c.right])})});return i&&rt(e),r}function jr(t,e){return t[1]>e[0]&&e[1]>t[0]}var xi,Oa={mixins:[le,_t,Gn],args:"title",props:{delay:Number,title:String},data:{pos:"top",title:"",delay:0,animation:["uk-animation-scale-up"],duration:100,cls:"uk-active",clsPos:"uk-tooltip"},beforeConnect:function(){this._hasTitle=te(this.$el,"title"),M(this.$el,"title",""),this.updateAria(!1),Fa(this.$el)},disconnected:function(){this.hide(),M(this.$el,"title",this._hasTitle?this.title:null)},methods:{show:function(){var t=this;this.isToggled(this.tooltip||null)||!this.title||(this._unbind=X(document,"show keydown "+dt,this.hide,!1,function(e){return e.type===dt&&!G(e.target,t.$el)||e.type==="keydown"&&e.keyCode===27||e.type==="show"&&e.detail[0]!==t&&e.detail[0].$name===t.$name}),clearTimeout(this.showTimer),this.showTimer=setTimeout(this._show,this.delay))},hide:function(){var t=this;Y(this.$el,"input:focus")||(clearTimeout(this.showTimer),!!this.isToggled(this.tooltip||null)&&this.toggleElement(this.tooltip,!1,!1).then(function(){t.tooltip=rt(t.tooltip),t._unbind()}))},_show:function(){var t=this;this.tooltip=K(this.container,'<div class="'+this.clsPos+'"> <div class="'+this.clsPos+'-inner">'+this.title+"</div> </div>"),A(this.tooltip,"toggled",function(e,i){t.updateAria(i),!!i&&(t.positionAt(t.tooltip,t.$el),t.origin=t.getAxis()==="y"?Me(t.dir)+"-"+t.align:t.align+"-"+Me(t.dir))}),this.toggleElement(this.tooltip,!0)},updateAria:function(t){M(this.$el,"aria-expanded",t)}},events:(xi={focus:"show",blur:"hide"},xi[Lt+" "+re]=function(t){Et(t)||this[t.type===Lt?"show":"hide"]()},xi[dt]=function(t){Et(t)&&this.show()},xi)};function Fa(t){Ve(t)||M(t,"tabindex","0")}var La={props:{allow:String,clsDragover:String,concurrent:Number,maxSize:Number,method:String,mime:String,msgInvalidMime:String,msgInvalidName:String,msgInvalidSize:String,multiple:Boolean,name:String,params:Object,type:String,url:String},data:{allow:!1,clsDragover:"uk-dragover",concurrent:1,maxSize:0,method:"POST",mime:!1,msgInvalidMime:"Invalid File Type: %s",msgInvalidName:"Invalid File Name: %s",msgInvalidSize:"Invalid File Size: %s Kilobytes Max",multiple:!1,name:"files[]",params:{},type:"",url:"",abort:D,beforeAll:D,beforeSend:D,complete:D,completeAll:D,error:D,fail:D,load:D,loadEnd:D,loadStart:D,progress:D},events:{change:function(t){!Y(t.target,'input[type="file"]')||(t.preventDefault(),t.target.files&&this.upload(t.target.files),t.target.value="")},drop:function(t){yi(t);var e=t.dataTransfer;!e||!e.files||(N(this.$el,this.clsDragover),this.upload(e.files))},dragenter:function(t){yi(t)},dragover:function(t){yi(t),y(this.$el,this.clsDragover)},dragleave:function(t){yi(t),N(this.$el,this.clsDragover)}},methods:{upload:function(t){var e=this;if(!!t.length){m(this.$el,"upload",[t]);for(var i=0;i<t.length;i++){if(this.maxSize&&this.maxSize*1e3<t[i].size){this.fail(this.msgInvalidSize.replace("%s",this.maxSize));return}if(this.allow&&!qr(this.allow,t[i].name)){this.fail(this.msgInvalidName.replace("%s",this.allow));return}if(this.mime&&!qr(this.mime,t[i].type)){this.fail(this.msgInvalidMime.replace("%s",this.mime));return}}this.multiple||(t=[t[0]]),this.beforeAll(this,t);var n=Wa(t,this.concurrent),r=function(s){var o=new FormData;s.forEach(function(u){return o.append(e.name,u)});for(var a in e.params)o.append(a,e.params[a]);Je(e.url,{data:o,method:e.method,responseType:e.type,beforeSend:function(u){var c=u.xhr;return c.upload&&A(c.upload,"progress",e.progress),["loadStart","load","loadEnd","abort"].forEach(function(f){return A(c,f.toLowerCase(),e[f])}),e.beforeSend(u)}}).then(function(u){e.complete(u),n.length?r(n.shift()):e.completeAll(u)},function(u){return e.error(u)})};r(n.shift())}}}};function qr(t,e){return e.match(new RegExp("^"+t.replace(/\//g,"\\/").replace(/\*\*/g,"(\\/[^\\/]+)*").replace(/\*/g,"[^\\/]+").replace(/((?!\\))\?/g,"$1.")+"$","i"))}function Wa(t,e){for(var i=[],n=0;n<t.length;n+=e){for(var r=[],s=0;s<e;s++)r.push(t[n+s]);i.push(r)}return i}function yi(t){t.preventDefault(),t.stopPropagation()}var Ra=Object.freeze({__proto__:null,Countdown:Qo,Filter:ia,Lightbox:ga,LightboxPanel:Pr,Notification:ma,Parallax:ka,Slider:Ea,SliderParallax:Fr,Slideshow:Aa,SlideshowParallax:Fr,Sortable:Pa,Tooltip:Oa,Upload:La});return nt(Ra,function(t,e){return et.component(e,t)}),et});
