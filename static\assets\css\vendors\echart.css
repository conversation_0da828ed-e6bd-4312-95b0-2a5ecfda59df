/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/
html,
body,
#main,
body > .main {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: arial;
}

.test-title {
  font-weight: normal;
  font-size: 16px;
}

.test-title-inner {
  display: inline-block;
  *display: inline;
  zoom: 1;
  text-align: left;
}

.test-title strong {
  color: yellow;
  font-weight: 700;
  text-shadow: -1px 0 #000, 0 1px #000, 1px 0 #000, 0 -1px #000;
  padding-left: 2px;
  padding-right: 2px;
}

.test-buttons button {
  margin: 10px 5px;
}

.test-chart-block {
  position: relative;
}

.test-chart-block-has-right {
  overflow: hidden;
}

.test-chart-block-has-right .test-chart-block-right {
  position: absolute;
  right: 10px;
  background: #fff;
  z-index: 99;
  width: 300px;
  max-height: 99%;
  border-left: 1px solid #ddd;
  border-bottom: 1px solid #ddd;
}

.test-chart-block-has-right .test-chart-block-left {
  margin-right: 320px;
}

.test-info {
  padding-left: 10px;
  overflow: auto;
}

pre.test-print-object {
  font-size: 12px;
  font-family: Menlo, Monaco, "Courier New", monospace;
}

.test-chart {
  height: 400px;
}

.test-data-table {
  position: relative;
  text-align: center;
}

.test-data-table table {
  display: inline-block;
  vertical-align: top;
  border: 1px solid #ccc;
  border-spacing: 0;
  margin: 30px 15px;
}

.test-data-table td {
  border: 1px solid #ccc;
  color: #777;
  padding: 3px 5px;
  font-size: 13px;
}

td.test-data-table-key {
  font-size: 12px;
  color: rgb(69, 162, 238);
}

.record-canvas .content-area {
  display: none;
  position: absolute;
  background: #fff;
  left: 10px;
  top: 20px;
  border: 2px solid #000;
  padding: 10px;
  z-index: 9999;
  box-shadow: 0 0 3px #000;
}

.record-canvas textarea {
  width: 300px;
  height: 500px;
}

.control-frame-btn-panel {
  position: fixed;
  top: 10px;
  left: 10px;
  box-shadow: 0 0 3px #000;
  background: green;
  padding: 5px;
}

.control-frame-btn-panel .control-frame-info {
  display: block;
  color: #fff;
  font-size: 10px;
}