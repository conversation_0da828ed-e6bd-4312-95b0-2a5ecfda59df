// Default ckeditor
(function () {
  CKEDITOR.replace("editor1", {
    on: {
      contentDom: function (evt) {
        // Allow custom context menu only with table elemnts.
        evt.editor.editable().on(
          "contextmenu",
          function (contextEvent) {
            var path = evt.editor.elementPath();

            if (!path.contains("table")) {
              contextEvent.cancel();
            }
          },
          null,
          null,
          5
        );
      },
    },
  });

  // Inline ckeditor
  CKEDITOR.disableAutoInline = true;
  //init the area to be done
  CKEDITOR.inline("area1", {
    toolbar: [
      {
        name: "basicstyles",
        groups: ["basicstyles"],
        items: ["Format", "Bold", "Italic", "Underline"],
      },
      {
        name: "paragraph",
        groups: ["list", "indent", "blocks", "align", "bidi"],
        items: [
          "NumberedList",
          "BulletedList",
          "JustifyLeft",
          "JustifyCenter",
          "JustifyRight",
        ],
      },
      {
        name: "links",
        items: ["<PERSON>", "Unlink"],
      },
    ],
    fillEmptyBlocks: false,
    autoParagraph: false,
  });
})();
